version: '3.8'

services:
  # Redis 服务
  redis:
    image: redis:7-alpine
    container_name: file-processor-redis-dev
    restart: unless-stopped
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes
    networks:
      - file-processor-dev-network

  # 后端开发服务
  backend-dev:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: file-processor-backend-dev
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=development
      - FLASK_DEBUG=true
      - FLASK_APP=app.py           # 指定入口（按你的项目调整）
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./backend:/app
      - ./backend/uploads:/app/uploads
      - ./backend/output:/app/output
      - ./backend/logs:/app/logs
    depends_on:
      - redis
    networks:
      - file-processor-dev-network
    command: flask run --host=0.0.0.0 --port=5000 --reload

  # Celery Worker 开发服务
  celery-worker-dev:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: file-processor-worker-dev
    restart: unless-stopped
    command: celery -A app.celery worker --loglevel=debug --concurrency=2 --pool=solo --autoreload
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./backend:/app
      - ./backend/uploads:/app/uploads
      - ./backend/output:/app/output
      - ./backend/logs:/app/logs
    depends_on:
      - redis
      - backend-dev
    networks:
      - file-processor-dev-network

  # 前端开发服务
  frontend-dev:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: file-processor-frontend-dev
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - REACT_APP_API_BASE_URL=http://localhost:5000/api
      - CHOKIDAR_USEPOLLING=true
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend-dev
    networks:
      - file-processor-dev-network
    command: npm run dev

volumes:
  redis_dev_data:
    driver: local

networks:
  file-processor-dev-network:
    driver: bridge
