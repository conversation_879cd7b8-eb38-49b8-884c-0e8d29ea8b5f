#!/bin/bash

# 文件处理器 Docker 部署脚本

set -e

echo "========================================="
echo "文件处理器 Docker 部署脚本"
echo "========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 和 Docker Compose
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_info "依赖检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p backend/uploads
    mkdir -p backend/output
    mkdir -p backend/logs
    mkdir -p nginx/ssl
    
    log_info "目录创建完成"
}

# 构建镜像
build_images() {
    log_info "构建 Docker 镜像..."
    
    # 构建后端镜像
    log_info "构建后端镜像..."
    docker-compose build backend
    
    # 构建前端镜像
    log_info "构建前端镜像..."
    docker-compose build frontend
    
    log_info "镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动所有服务
    docker-compose up -d
    
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    check_services
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 检查 Redis
    if docker-compose exec redis redis-cli ping | grep -q PONG; then
        log_info "✅ Redis 服务正常"
    else
        log_error "❌ Redis 服务异常"
    fi
    
    # 检查后端
    if curl -f http://localhost:5000/api/health &> /dev/null; then
        log_info "✅ 后端服务正常"
    else
        log_warn "⚠️  后端服务可能还在启动中..."
    fi
    
    # 检查前端
    if curl -f http://localhost:3000 &> /dev/null; then
        log_info "✅ 前端服务正常"
    else
        log_warn "⚠️  前端服务可能还在启动中..."
    fi
    
    # 检查 Nginx
    if curl -f http://localhost/health &> /dev/null; then
        log_info "✅ Nginx 服务正常"
    else
        log_warn "⚠️  Nginx 服务可能还在启动中..."
    fi
}

# 显示服务信息
show_info() {
    echo ""
    echo "========================================="
    echo "部署完成！"
    echo "========================================="
    echo ""
    echo "服务访问地址："
    echo "  🌐 主应用:     http://localhost"
    echo "  📱 前端:       http://localhost:3000"
    echo "  🔧 后端 API:   http://localhost:5000"
    echo "  📊 健康检查:   http://localhost:5000/api/health"
    echo ""
    echo "管理命令："
    echo "  查看日志:     docker-compose logs -f"
    echo "  停止服务:     docker-compose down"
    echo "  重启服务:     docker-compose restart"
    echo "  查看状态:     docker-compose ps"
    echo ""
    echo "文件位置："
    echo "  上传文件:     ./backend/uploads/"
    echo "  输出文件:     ./backend/output/"
    echo "  日志文件:     ./backend/logs/"
    echo ""
}

# 主函数
main() {
    case "${1:-deploy}" in
        "deploy")
            check_dependencies
            create_directories
            build_images
            start_services
            show_info
            ;;
        "start")
            log_info "启动服务..."
            docker-compose up -d
            check_services
            ;;
        "stop")
            log_info "停止服务..."
            docker-compose down
            ;;
        "restart")
            log_info "重启服务..."
            docker-compose restart
            check_services
            ;;
        "logs")
            docker-compose logs -f
            ;;
        "status")
            docker-compose ps
            check_services
            ;;
        "clean")
            log_warn "清理所有容器和镜像..."
            docker-compose down -v --rmi all
            ;;
        "help")
            echo "用法: $0 [命令]"
            echo ""
            echo "命令:"
            echo "  deploy   - 完整部署 (默认)"
            echo "  start    - 启动服务"
            echo "  stop     - 停止服务"
            echo "  restart  - 重启服务"
            echo "  logs     - 查看日志"
            echo "  status   - 查看状态"
            echo "  clean    - 清理所有"
            echo "  help     - 显示帮助"
            ;;
        *)
            log_error "未知命令: $1"
            echo "使用 '$0 help' 查看帮助"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
