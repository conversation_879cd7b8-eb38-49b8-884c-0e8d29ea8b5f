import React, { useState, useEffect } from 'react'
import { Clock, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react'
import ProgressBar from './ProgressBar'
import StageIndicator from './StageIndicator'
import './TaskProgressCard.css'

const TaskProgressCard = ({ 
  taskName, 
  taskResults, 
  startTime = null 
}) => {
  const [elapsedTime, setElapsedTime] = useState(0)

  // 计算已用时间
  useEffect(() => {
    if (!startTime) return

    const interval = setInterval(() => {
      const now = new Date()
      const start = new Date(startTime)
      const elapsed = Math.floor((now - start) / 1000)
      setElapsedTime(elapsed)
    }, 1000)

    return () => clearInterval(interval)
  }, [startTime])

  // 计算任务整体状态和进度
  const getTaskStatus = () => {
    if (!taskResults || taskResults.length === 0) {
      return { status: 'pending', progress: 0, stage: 'pending' }
    }

    const completedCount = taskResults.filter(r => r.status === 'SUCCESS').length
    const failedCount = taskResults.filter(r => r.status === 'FAILURE' || r.status === 'ERROR').length
    const processingCount = taskResults.filter(r => r.status === 'PROCESSING').length

    const progress = (completedCount / taskResults.length) * 100

    // 确定整体状态
    if (failedCount > 0 && completedCount + failedCount === taskResults.length) {
      return { 
        status: 'error', 
        progress: 100, 
        stage: 'completed',
        completedCount,
        failedCount,
        processingCount
      }
    } else if (completedCount === taskResults.length) {
      return { 
        status: 'success', 
        progress: 100, 
        stage: 'completed',
        completedCount,
        failedCount,
        processingCount
      }
    } else if (processingCount > 0 || completedCount > 0) {
      return { 
        status: 'processing', 
        progress, 
        stage: 'processing',
        completedCount,
        failedCount,
        processingCount
      }
    } else {
      return { 
        status: 'pending', 
        progress: 0, 
        stage: 'upload',
        completedCount,
        failedCount,
        processingCount
      }
    }
  }

  const taskStatus = getTaskStatus()

  // 格式化时间显示
  const formatTime = (seconds) => {
    if (seconds < 60) {
      return `${seconds}秒`
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      return `${minutes}分${remainingSeconds}秒`
    } else {
      const hours = Math.floor(seconds / 3600)
      const minutes = Math.floor((seconds % 3600) / 60)
      return `${hours}小时${minutes}分钟`
    }
  }

  // 获取状态图标
  const getStatusIcon = () => {
    switch (taskStatus.status) {
      case 'success':
        return <CheckCircle className="status-icon success" size={20} />
      case 'error':
        return <AlertCircle className="status-icon error" size={20} />
      case 'processing':
        return <RefreshCw className="status-icon processing spinning" size={20} />
      default:
        return <Clock className="status-icon pending" size={20} />
    }
  }

  return (
    <div className={`task-progress-card ${taskStatus.status}`}>
      <div className="task-header">
        <div className="task-title">
          {getStatusIcon()}
          <span className="task-name">{taskName}</span>
        </div>
        <div className="task-meta">
          {startTime && (
            <div className="elapsed-time">
              <Clock size={14} />
              <span>{formatTime(elapsedTime)}</span>
            </div>
          )}
        </div>
      </div>

      <div className="task-progress">
        <ProgressBar 
          progress={taskStatus.progress}
          status={taskStatus.status}
          size="small"
          animated={taskStatus.status === 'processing'}
        />
      </div>

      <div className="task-stage">
        <StageIndicator 
          currentStage={taskStatus.stage}
          status={taskStatus.status}
        />
      </div>

      <div className="task-summary">
        <div className="summary-stats">
          <span className="stat success">✓ {taskStatus.completedCount}</span>
          <span className="stat error">✗ {taskStatus.failedCount}</span>
          <span className="stat processing">⟳ {taskStatus.processingCount}</span>
          <span className="stat total">总计 {taskResults.length}</span>
        </div>
      </div>
    </div>
  )
}

export default TaskProgressCard
