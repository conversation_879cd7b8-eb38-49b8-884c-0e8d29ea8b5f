# 开发环境 Dockerfile
FROM python:3.12-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    # OpenCV 依赖
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libglib2.0-0 \
    libgtk-3-0 \
    # FFmpeg 依赖
    ffmpeg \
    # 开发工具
    curl \
    wget \
    vim \
    git \
    && rm -rf /var/lib/apt/lists/*

# 复制 requirements.txt
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 安装开发依赖
RUN pip install --no-cache-dir \
    flask-debugtoolbar \
    watchdog \
    ipdb

# 创建必要的目录
RUN mkdir -p uploads output logs

# 设置环境变量
ENV PYTHONPATH=/app
ENV FLASK_APP=app.py
ENV FLASK_ENV=development
ENV FLASK_DEBUG=true

# 暴露端口
EXPOSE 5000

# 开发模式启动命令
CMD ["python", "app.py"]
