"""
图像拼接处理任务
"""
import cv2
import numpy as np
from celery import current_task
import os
import sys
import logging
import time

logger = logging.getLogger(__name__)


def safe_int(value, default=0):
    """安全地将值转换为整数"""
    if isinstance(value, int):
        return value
    if isinstance(value, (float, str)):
        try:
            return int(float(value))
        except (ValueError, TypeError):
            return default
    return default


def image_stitching_task(self, file_paths, parameters):
    """图像拼接处理"""
    try:
        self.update_state(state='PROGRESS',
                         meta={'current': 10, 'total': 100,
                               'status': 'Loading images...'})

        # 读取所有图像
        images = []
        for i, file_path in enumerate(file_paths):
            image = cv2.imread(file_path)
            if image is None:
                raise ValueError(f"Cannot load image from {file_path}")
            images.append(image)
            
            progress = 10 + (i + 1) * 20 // len(file_paths)
            self.update_state(state='PROGRESS',
                             meta={'current': progress, 'total': 100,
                                   'status': f'Loaded image {i+1}/{len(file_paths)}'})

        if len(images) < 2:
            raise ValueError("At least 2 images are required for stitching")

        self.update_state(state='PROGRESS',
                         meta={'current': 40, 'total': 100,
                               'status': 'Initializing stitcher...'})

        # 获取拼接模式
        mode = parameters.get('mode', 'panorama')
        
        # 创建拼接器
        if mode == 'scans':
            stitcher = cv2.Stitcher.create(cv2.Stitcher_SCANS)
        else:
            stitcher = cv2.Stitcher.create(cv2.Stitcher_PANORAMA)

        self.update_state(state='PROGRESS',
                         meta={'current': 60, 'total': 100,
                               'status': 'Stitching images...'})

        # 执行拼接
        status, stitched = stitcher.stitch(images)

        if status != cv2.Stitcher_OK:
            error_messages = {
                cv2.Stitcher_ERR_NEED_MORE_IMGS: "Need more images",
                cv2.Stitcher_ERR_HOMOGRAPHY_EST_FAIL: "Homography estimation failed",
                cv2.Stitcher_ERR_CAMERA_PARAMS_ADJUST_FAIL: "Camera parameters adjustment failed"
            }
            error_msg = error_messages.get(status, f"Stitching failed with status {status}")
            raise ValueError(error_msg)

        self.update_state(state='PROGRESS',
                         meta={'current': 80, 'total': 100,
                               'status': 'Saving result...'})

        # 保存结果
        output_dir = 'output'
        os.makedirs(output_dir, exist_ok=True)
        timestamp = int(time.time() * 1000)
        output_filename = f'stitched_{len(images)}_images_{timestamp}.jpg'
        output_path = os.path.join(output_dir, output_filename)
        cv2.imwrite(output_path, stitched)

        logger.info(f"Image stitching completed: {len(images)} images stitched")

        result = {
            'status': 'success',
            'output_path': output_path,
            'output_filename': output_filename,
            'original_paths': file_paths,
            'images_count': len(images),
            'mode': mode
        }

        # 设置任务状态为成功
        self.update_state(state='SUCCESS', meta=result)
        return result

    except Exception as e:
        logger.error(f"Image stitching error: {str(e)}")
        self.update_state(state='FAILURE',
                         meta={'error': str(e)})
        raise