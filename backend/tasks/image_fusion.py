"""
图像融合处理任务
"""
import cv2
import numpy as np
import os
import sys
import time
import logging
from celery import current_task

logger = logging.getLogger(__name__)


def safe_float(value, default=0.0):
    """安全地将值转换为浮点数"""
    if isinstance(value, (int, float)):
        return float(value)
    if isinstance(value, str):
        try:
            return float(value)
        except (ValueError, TypeError):
            return default
    return default


def image_fusion_task(self, file1_path, file2_path, **parameters):
    """图像融合处理（需要两张图像）"""
    try:
        self.update_state(state='PROGRESS',
                         meta={'current': 10, 'total': 100,
                               'status': 'Loading images...'})

        # 读取第一张图像
        image1 = cv2.imread(file1_path)
        if image1 is None:
            raise ValueError(f"Cannot load first image from {file1_path}")

        # 读取第二张图像
        image2 = cv2.imread(file2_path)
        if image2 is None:
            raise ValueError(f"Cannot load second image from {file2_path}")

        self.update_state(state='PROGRESS',
                         meta={'current': 30, 'total': 100,
                               'status': 'Resizing images...'})

        # 调整图像大小使其一致
        height = min(image1.shape[0], image2.shape[0])
        width = min(image1.shape[1], image2.shape[1])
        image1 = cv2.resize(image1, (width, height))
        image2 = cv2.resize(image2, (width, height))

        self.update_state(state='PROGRESS',
                         meta={'current': 60, 'total': 100,
                               'status': 'Fusing images...'})

        # 获取融合权重
        weight1 = safe_float(parameters.get('weight1', 0.5), 0.5)
        weight2 = safe_float(parameters.get('weight2', 0.5), 0.5)

        # 图像融合
        fused = cv2.addWeighted(image1, weight1, image2, weight2, 0)

        self.update_state(state='PROGRESS',
                         meta={'current': 80, 'total': 100,
                               'status': 'Saving result...'})

        # 保存结果
        output_dir = 'output'
        os.makedirs(output_dir, exist_ok=True)
        # 生成唯一文件名避免缓存问题
        timestamp = int(time.time() * 1000)
        base_name1 = os.path.splitext(os.path.basename(file1_path))[0]
        base_name2 = os.path.splitext(os.path.basename(file2_path))[0]
        ext = os.path.splitext(os.path.basename(file1_path))[1]
        output_filename = f'fused_{base_name1}_{base_name2}_{timestamp}{ext}'
        output_path = os.path.join(output_dir, output_filename)
        cv2.imwrite(output_path, fused)

        result = {
            'status': 'success',
            'output_path': output_path,
            'output_filename': output_filename,
            'file1_path': file1_path,
            'file2_path': file2_path,
            'weight1': weight1,
            'weight2': weight2
        }

        # 设置任务状态为成功
        self.update_state(state='SUCCESS', meta=result)
        return result

    except Exception as e:
        error_msg = str(e)
        logger.error(f"Image fusion task failed: {error_msg}")
        raise Exception(f"Image fusion failed: {error_msg}")