('/home/<USER>/projects/homework/backend/dist/FileProcessorBackendSync',
 True,
 False,
 False,
 None,
 None,
 False,
 False,
 None,
 True,
 False,
 None,
 None,
 None,
 '/home/<USER>/projects/homework/backend/build/desktop_app/FileProcessorBackendSync.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   '/home/<USER>/projects/homework/backend/build/desktop_app/PYZ-00.pyz',
   'PYZ'),
  ('struct',
   '/home/<USER>/projects/homework/backend/build/desktop_app/localpycs/struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   '/home/<USER>/projects/homework/backend/build/desktop_app/localpycs/pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   '/home/<USER>/projects/homework/backend/build/desktop_app/localpycs/pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   '/home/<USER>/projects/homework/backend/build/desktop_app/localpycs/pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PyInstaller/loader/pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('desktop_app_sync',
   '/home/<USER>/projects/homework/backend/desktop_app_sync.py',
   'PYSOURCE'),
  ('imageio_ffmpeg/binaries/ffmpeg-linux-x86_64-v7.0.2',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio_ffmpeg/binaries/ffmpeg-linux-x86_64-v7.0.2',
   'BINARY'),
  ('libpython3.12.so', '/usr/lib/x86_64-linux-gnu/libpython3.12.so', 'BINARY'),
  ('cv2/python-3.12/cv2.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/dist-packages/cv2/python-3.12/cv2.cpython-312-x86_64-linux-gnu.so',
   'BINARY'),
  ('lib-dynload/_contextvars.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_contextvars.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_decimal.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_hashlib.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_lzma.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_bz2.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/resource.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_queue.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/mmap.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_posixshmem.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_ctypes.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_multiprocessing.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/termios.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_ssl.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/readline.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_json.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('psutil/_psutil_posix.abi3.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/psutil/_psutil_posix.abi3.so',
   'EXTENSION'),
  ('psutil/_psutil_linux.abi3.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/psutil/_psutil_linux.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_curses.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_curses.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/core/_multiarray_umath.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/_multiarray_umath.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/linalg/_umath_linalg.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/linalg/_umath_linalg.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_imaging.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/_imaging.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_imagingmath.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/_imagingmath.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('charset_normalizer/md__mypyc.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/charset_normalizer/md__mypyc.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_multibytecodec.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('charset_normalizer/md.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/charset_normalizer/md.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_webp.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/_webp.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_imagingtk.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/_imagingtk.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_avif.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/_avif.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_imagingcms.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/_imagingcms.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_zoneinfo.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_zoneinfo.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_gdbm.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_gdbm.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_dbm.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_dbm.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_asyncio.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('markupsafe/_speedups.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/markupsafe/_speedups.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_codecs_jp.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_codecs_kr.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_codecs_iso2022.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_codecs_cn.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_codecs_tw.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_codecs_hk.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/core/_multiarray_tests.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/_multiarray_tests.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/random/mtrand.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/mtrand.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/random/_sfc64.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/_sfc64.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/random/_philox.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/_philox.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/random/_pcg64.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/_pcg64.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/random/_mt19937.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/_mt19937.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/random/bit_generator.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/bit_generator.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/random/_generator.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/_generator.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/random/_bounded_integers.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/_bounded_integers.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/random/_common.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/_common.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/fft/_pocketfft_internal.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/fft/_pocketfft_internal.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('libexpat.so.1', '/usr/lib/x86_64-linux-gnu/libexpat.so.1', 'BINARY'),
  ('libz.so.1', '/usr/lib/x86_64-linux-gnu/libz.so.1', 'BINARY'),
  ('libpcre2-8.so.0', '/usr/lib/x86_64-linux-gnu/libpcre2-8.so.0', 'BINARY'),
  ('libxcb-render.so.0',
   '/usr/lib/x86_64-linux-gnu/libxcb-render.so.0',
   'BINARY'),
  ('libopencore-amrnb.so.0',
   '/usr/lib/x86_64-linux-gnu/libopencore-amrnb.so.0',
   'BINARY'),
  ('libva-drm.so.2', '/usr/lib/x86_64-linux-gnu/libva-drm.so.2', 'BINARY'),
  ('libhogweed.so.6', '/usr/lib/x86_64-linux-gnu/libhogweed.so.6', 'BINARY'),
  ('libgssapi_krb5.so.2',
   '/usr/lib/x86_64-linux-gnu/libgssapi_krb5.so.2',
   'BINARY'),
  ('libopencv_saliency.so.413',
   '/usr/local/lib/libopencv_saliency.so.413',
   'BINARY'),
  ('libopencv_cudaoptflow.so.413',
   '/usr/local/lib/libopencv_cudaoptflow.so.413',
   'BINARY'),
  ('libjpeg.so.8', '/usr/lib/x86_64-linux-gnu/libjpeg.so.8', 'BINARY'),
  ('libwebp.so.7', '/usr/lib/x86_64-linux-gnu/libwebp.so.7', 'BINARY'),
  ('libXrender.so.1', '/usr/lib/x86_64-linux-gnu/libXrender.so.1', 'BINARY'),
  ('librav1e.so.0', '/usr/lib/x86_64-linux-gnu/librav1e.so.0', 'BINARY'),
  ('libxml2.so.2', '/usr/lib/x86_64-linux-gnu/libxml2.so.2', 'BINARY'),
  ('libepoxy.so.0', '/usr/lib/x86_64-linux-gnu/libepoxy.so.0', 'BINARY'),
  ('libfreetype.so.6', '/usr/lib/x86_64-linux-gnu/libfreetype.so.6', 'BINARY'),
  ('libopencv_features2d.so.413',
   '/usr/local/lib/libopencv_features2d.so.413',
   'BINARY'),
  ('libvo-amrwbenc.so.0',
   '/usr/lib/x86_64-linux-gnu/libvo-amrwbenc.so.0',
   'BINARY'),
  ('libogg.so.0', '/usr/lib/x86_64-linux-gnu/libogg.so.0', 'BINARY'),
  ('libopencv_reg.so.413', '/usr/local/lib/libopencv_reg.so.413', 'BINARY'),
  ('libvorbis.so.0', '/usr/lib/x86_64-linux-gnu/libvorbis.so.0', 'BINARY'),
  ('libunwind.so.8', '/usr/lib/x86_64-linux-gnu/libunwind.so.8', 'BINARY'),
  ('libopencv_highgui.so.413',
   '/usr/local/lib/libopencv_highgui.so.413',
   'BINARY'),
  ('libvdpau.so.1', '/usr/lib/x86_64-linux-gnu/libvdpau.so.1', 'BINARY'),
  ('liblcms2.so.2', '/usr/lib/x86_64-linux-gnu/liblcms2.so.2', 'BINARY'),
  ('libopencv_cudabgsegm.so.413',
   '/usr/local/lib/libopencv_cudabgsegm.so.413',
   'BINARY'),
  ('libva.so.2', '/usr/lib/x86_64-linux-gnu/libva.so.2', 'BINARY'),
  ('libnppitc.so.12', '/usr/lib/x86_64-linux-gnu/libnppitc.so.12', 'BINARY'),
  ('libbz2.so.1.0', '/usr/lib/x86_64-linux-gnu/libbz2.so.1.0', 'BINARY'),
  ('libopencv_rgbd.so.413', '/usr/local/lib/libopencv_rgbd.so.413', 'BINARY'),
  ('libopencv_cudafilters.so.413',
   '/usr/local/lib/libopencv_cudafilters.so.413',
   'BINARY'),
  ('libsoxr.so.0', '/usr/lib/x86_64-linux-gnu/libsoxr.so.0', 'BINARY'),
  ('libelf.so.1', '/usr/lib/x86_64-linux-gnu/libelf.so.1', 'BINARY'),
  ('libopencv_rapid.so.413', '/usr/local/lib/libopencv_rapid.so.413', 'BINARY'),
  ('libmpg123.so.0', '/usr/lib/x86_64-linux-gnu/libmpg123.so.0', 'BINARY'),
  ('libicudata.so.74', '/usr/lib/x86_64-linux-gnu/libicudata.so.74', 'BINARY'),
  ('libzstd.so.1', '/usr/lib/x86_64-linux-gnu/libzstd.so.1', 'BINARY'),
  ('libatspi.so.0', '/usr/lib/x86_64-linux-gnu/libatspi.so.0', 'BINARY'),
  ('libpgm-5.3.so.0', '/usr/lib/x86_64-linux-gnu/libpgm-5.3.so.0', 'BINARY'),
  ('libshine.so.3', '/usr/lib/x86_64-linux-gnu/libshine.so.3', 'BINARY'),
  ('libcom_err.so.2', '/usr/lib/x86_64-linux-gnu/libcom_err.so.2', 'BINARY'),
  ('libmp3lame.so.0', '/usr/lib/x86_64-linux-gnu/libmp3lame.so.0', 'BINARY'),
  ('libcrypto.so.3', '/usr/lib/x86_64-linux-gnu/libcrypto.so.3', 'BINARY'),
  ('libXi.so.6', '/usr/lib/x86_64-linux-gnu/libXi.so.6', 'BINARY'),
  ('libopencv_photo.so.413', '/usr/local/lib/libopencv_photo.so.413', 'BINARY'),
  ('libXext.so.6', '/usr/lib/x86_64-linux-gnu/libXext.so.6', 'BINARY'),
  ('libcairo.so.2', '/usr/lib/x86_64-linux-gnu/libcairo.so.2', 'BINARY'),
  ('libswresample.so.4',
   '/usr/lib/x86_64-linux-gnu/libswresample.so.4',
   'BINARY'),
  ('libx265.so.199', '/usr/lib/x86_64-linux-gnu/libx265.so.199', 'BINARY'),
  ('libopencv_ccalib.so.413',
   '/usr/local/lib/libopencv_ccalib.so.413',
   'BINARY'),
  ('libopencore-amrwb.so.0',
   '/usr/lib/x86_64-linux-gnu/libopencore-amrwb.so.0',
   'BINARY'),
  ('libgobject-2.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0',
   'BINARY'),
  ('libbrotlienc.so.1',
   '/usr/lib/x86_64-linux-gnu/libbrotlienc.so.1',
   'BINARY'),
  ('libgdk_pixbuf-2.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so.0',
   'BINARY'),
  ('libSvtAv1Enc.so.1',
   '/usr/lib/x86_64-linux-gnu/libSvtAv1Enc.so.1',
   'BINARY'),
  ('libX11-xcb.so.1', '/usr/lib/x86_64-linux-gnu/libX11-xcb.so.1', 'BINARY'),
  ('libXcomposite.so.1',
   '/usr/lib/x86_64-linux-gnu/libXcomposite.so.1',
   'BINARY'),
  ('libopencv_freetype.so.413',
   '/usr/local/lib/libopencv_freetype.so.413',
   'BINARY'),
  ('libcublasLt.so.12',
   '/usr/lib/x86_64-linux-gnu/libcublasLt.so.12',
   'BINARY'),
  ('libicuuc.so.74', '/usr/lib/x86_64-linux-gnu/libicuuc.so.74', 'BINARY'),
  ('libopencv_flann.so.413', '/usr/local/lib/libopencv_flann.so.413', 'BINARY'),
  ('libopencv_ml.so.413', '/usr/local/lib/libopencv_ml.so.413', 'BINARY'),
  ('libgsm.so.1', '/usr/lib/x86_64-linux-gnu/libgsm.so.1', 'BINARY'),
  ('libopencv_dnn_superres.so.413',
   '/usr/local/lib/libopencv_dnn_superres.so.413',
   'BINARY'),
  ('libpango-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libpango-1.0.so.0',
   'BINARY'),
  ('libblkid.so.1', '/usr/lib/x86_64-linux-gnu/libblkid.so.1', 'BINARY'),
  ('libtiff.so.6', '/usr/lib/x86_64-linux-gnu/libtiff.so.6', 'BINARY'),
  ('libatk-bridge-2.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libatk-bridge-2.0.so.0',
   'BINARY'),
  ('libopencv_stereo.so.413',
   '/usr/local/lib/libopencv_stereo.so.413',
   'BINARY'),
  ('libopencv_plot.so.413', '/usr/local/lib/libopencv_plot.so.413', 'BINARY'),
  ('libopencv_xfeatures2d.so.413',
   '/usr/local/lib/libopencv_xfeatures2d.so.413',
   'BINARY'),
  ('libIex-3_1.so.30', '/usr/lib/x86_64-linux-gnu/libIex-3_1.so.30', 'BINARY'),
  ('libopencv_bgsegm.so.413',
   '/usr/local/lib/libopencv_bgsegm.so.413',
   'BINARY'),
  ('libgstaudio-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgstaudio-1.0.so.0',
   'BINARY'),
  ('libsodium.so.23', '/usr/lib/x86_64-linux-gnu/libsodium.so.23', 'BINARY'),
  ('libnorm.so.1', '/usr/lib/x86_64-linux-gnu/libnorm.so.1', 'BINARY'),
  ('libpixman-1.so.0', '/usr/lib/x86_64-linux-gnu/libpixman-1.so.0', 'BINARY'),
  ('libgdk-3.so.0', '/usr/lib/x86_64-linux-gnu/libgdk-3.so.0', 'BINARY'),
  ('libopencv_videoio.so.413',
   '/usr/local/lib/libopencv_videoio.so.413',
   'BINARY'),
  ('libnppig.so.12', '/usr/lib/x86_64-linux-gnu/libnppig.so.12', 'BINARY'),
  ('libnppif.so.12', '/usr/lib/x86_64-linux-gnu/libnppif.so.12', 'BINARY'),
  ('libchromaprint.so.1',
   '/usr/lib/x86_64-linux-gnu/libchromaprint.so.1',
   'BINARY'),
  ('libXdmcp.so.6', '/usr/lib/x86_64-linux-gnu/libXdmcp.so.6', 'BINARY'),
  ('libnppial.so.12', '/usr/lib/x86_64-linux-gnu/libnppial.so.12', 'BINARY'),
  ('libtheoraenc.so.1',
   '/usr/lib/x86_64-linux-gnu/libtheoraenc.so.1',
   'BINARY'),
  ('libXfixes.so.3', '/usr/lib/x86_64-linux-gnu/libXfixes.so.3', 'BINARY'),
  ('libwebpmux.so.3', '/usr/lib/x86_64-linux-gnu/libwebpmux.so.3', 'BINARY'),
  ('liblz4.so.1', '/usr/lib/x86_64-linux-gnu/liblz4.so.1', 'BINARY'),
  ('libOpenCL.so.1', '/usr/lib/x86_64-linux-gnu/libOpenCL.so.1', 'BINARY'),
  ('libgmp.so.10', '/usr/lib/x86_64-linux-gnu/libgmp.so.10', 'BINARY'),
  ('libdbus-1.so.3', '/usr/lib/x86_64-linux-gnu/libdbus-1.so.3', 'BINARY'),
  ('libcudnn.so.9', '/usr/lib/x86_64-linux-gnu/libcudnn.so.9', 'BINARY'),
  ('libIlmThread-3_1.so.30',
   '/usr/lib/x86_64-linux-gnu/libIlmThread-3_1.so.30',
   'BINARY'),
  ('libsrt-gnutls.so.1.5',
   '/usr/lib/x86_64-linux-gnu/libsrt-gnutls.so.1.5',
   'BINARY'),
  ('libnppidei.so.12', '/usr/lib/x86_64-linux-gnu/libnppidei.so.12', 'BINARY'),
  ('libvpx.so.9', '/usr/lib/x86_64-linux-gnu/libvpx.so.9', 'BINARY'),
  ('libwebpdemux.so.2',
   '/usr/lib/x86_64-linux-gnu/libwebpdemux.so.2',
   'BINARY'),
  ('libgmodule-2.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgmodule-2.0.so.0',
   'BINARY'),
  ('libopencv_gapi.so.413', '/usr/local/lib/libopencv_gapi.so.413', 'BINARY'),
  ('libvpl.so.2', '/usr/lib/x86_64-linux-gnu/libvpl.so.2', 'BINARY'),
  ('librabbitmq.so.4', '/usr/lib/x86_64-linux-gnu/librabbitmq.so.4', 'BINARY'),
  ('libmbedcrypto.so.7',
   '/usr/lib/x86_64-linux-gnu/libmbedcrypto.so.7',
   'BINARY'),
  ('libdeflate.so.0', '/usr/lib/x86_64-linux-gnu/libdeflate.so.0', 'BINARY'),
  ('libnppc.so.12', '/usr/lib/x86_64-linux-gnu/libnppc.so.12', 'BINARY'),
  ('libopus.so.0', '/usr/lib/x86_64-linux-gnu/libopus.so.0', 'BINARY'),
  ('libdatrie.so.1', '/usr/lib/x86_64-linux-gnu/libdatrie.so.1', 'BINARY'),
  ('libopencv_signal.so.413',
   '/usr/local/lib/libopencv_signal.so.413',
   'BINARY'),
  ('libxcb-shm.so.0', '/usr/lib/x86_64-linux-gnu/libxcb-shm.so.0', 'BINARY'),
  ('libmount.so.1', '/usr/lib/x86_64-linux-gnu/libmount.so.1', 'BINARY'),
  ('libXcursor.so.1', '/usr/lib/x86_64-linux-gnu/libXcursor.so.1', 'BINARY'),
  ('libvorbisfile.so.3',
   '/usr/lib/x86_64-linux-gnu/libvorbisfile.so.3',
   'BINARY'),
  ('libopencv_fuzzy.so.413', '/usr/local/lib/libopencv_fuzzy.so.413', 'BINARY'),
  ('libaribb24.so.0', '/usr/lib/x86_64-linux-gnu/libaribb24.so.0', 'BINARY'),
  ('libhwy.so.1', '/usr/lib/x86_64-linux-gnu/libhwy.so.1', 'BINARY'),
  ('libudfread.so.0', '/usr/lib/x86_64-linux-gnu/libudfread.so.0', 'BINARY'),
  ('libgstbase-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgstbase-1.0.so.0',
   'BINARY'),
  ('libopencv_phase_unwrapping.so.413',
   '/usr/local/lib/libopencv_phase_unwrapping.so.413',
   'BINARY'),
  ('libssl.so.3', '/usr/lib/x86_64-linux-gnu/libssl.so.3', 'BINARY'),
  ('libgsttag-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgsttag-1.0.so.0',
   'BINARY'),
  ('libk5crypto.so.3', '/usr/lib/x86_64-linux-gnu/libk5crypto.so.3', 'BINARY'),
  ('libgstreamer-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgstreamer-1.0.so.0',
   'BINARY'),
  ('libopencv_surface_matching.so.413',
   '/usr/local/lib/libopencv_surface_matching.so.413',
   'BINARY'),
  ('libavcodec.so.60', '/usr/lib/x86_64-linux-gnu/libavcodec.so.60', 'BINARY'),
  ('libcap.so.2', '/usr/lib/x86_64-linux-gnu/libcap.so.2', 'BINARY'),
  ('libopencv_stitching.so.413',
   '/usr/local/lib/libopencv_stitching.so.413',
   'BINARY'),
  ('libfribidi.so.0', '/usr/lib/x86_64-linux-gnu/libfribidi.so.0', 'BINARY'),
  ('libgstapp-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgstapp-1.0.so.0',
   'BINARY'),
  ('libtwolame.so.0', '/usr/lib/x86_64-linux-gnu/libtwolame.so.0', 'BINARY'),
  ('libaom.so.3', '/usr/lib/x86_64-linux-gnu/libaom.so.3', 'BINARY'),
  ('libgme.so.0', '/usr/lib/x86_64-linux-gnu/libgme.so.0', 'BINARY'),
  ('libopencv_ximgproc.so.413',
   '/usr/local/lib/libopencv_ximgproc.so.413',
   'BINARY'),
  ('libopencv_imgproc.so.413',
   '/usr/local/lib/libopencv_imgproc.so.413',
   'BINARY'),
  ('libpangocairo-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so.0',
   'BINARY'),
  ('libxkbcommon.so.0',
   '/usr/lib/x86_64-linux-gnu/libxkbcommon.so.0',
   'BINARY'),
  ('libopenmpt.so.0', '/usr/lib/x86_64-linux-gnu/libopenmpt.so.0', 'BINARY'),
  ('liblzma.so.5', '/usr/lib/x86_64-linux-gnu/liblzma.so.5', 'BINARY'),
  ('libLerc.so.4', '/usr/lib/x86_64-linux-gnu/libLerc.so.4', 'BINARY'),
  ('libopencv_cudacodec.so.413',
   '/usr/local/lib/libopencv_cudacodec.so.413',
   'BINARY'),
  ('libp11-kit.so.0', '/usr/lib/x86_64-linux-gnu/libp11-kit.so.0', 'BINARY'),
  ('libdav1d.so.7', '/usr/lib/x86_64-linux-gnu/libdav1d.so.7', 'BINARY'),
  ('libopencv_cudaobjdetect.so.413',
   '/usr/local/lib/libopencv_cudaobjdetect.so.413',
   'BINARY'),
  ('libopencv_quality.so.413',
   '/usr/local/lib/libopencv_quality.so.413',
   'BINARY'),
  ('libvorbisenc.so.2',
   '/usr/lib/x86_64-linux-gnu/libvorbisenc.so.2',
   'BINARY'),
  ('librsvg-2.so.2', '/usr/lib/x86_64-linux-gnu/librsvg-2.so.2', 'BINARY'),
  ('libpangoft2-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libpangoft2-1.0.so.0',
   'BINARY'),
  ('libopencv_objdetect.so.413',
   '/usr/local/lib/libopencv_objdetect.so.413',
   'BINARY'),
  ('libswscale.so.7', '/usr/lib/x86_64-linux-gnu/libswscale.so.7', 'BINARY'),
  ('libopencv_mcc.so.413', '/usr/local/lib/libopencv_mcc.so.413', 'BINARY'),
  ('libavformat.so.60',
   '/usr/lib/x86_64-linux-gnu/libavformat.so.60',
   'BINARY'),
  ('libopencv_img_hash.so.413',
   '/usr/local/lib/libopencv_img_hash.so.413',
   'BINARY'),
  ('libfontconfig.so.1',
   '/usr/lib/x86_64-linux-gnu/libfontconfig.so.1',
   'BINARY'),
  ('libnppicc.so.12', '/usr/lib/x86_64-linux-gnu/libnppicc.so.12', 'BINARY'),
  ('libnuma.so.1', '/usr/lib/x86_64-linux-gnu/libnuma.so.1', 'BINARY'),
  ('libopencv_tracking.so.413',
   '/usr/local/lib/libopencv_tracking.so.413',
   'BINARY'),
  ('libopencv_face.so.413', '/usr/local/lib/libopencv_face.so.413', 'BINARY'),
  ('libavutil.so.58', '/usr/lib/x86_64-linux-gnu/libavutil.so.58', 'BINARY'),
  ('libnppist.so.12', '/usr/lib/x86_64-linux-gnu/libnppist.so.12', 'BINARY'),
  ('libzmq.so.5', '/usr/lib/x86_64-linux-gnu/libzmq.so.5', 'BINARY'),
  ('libstdc++.so.6', '/usr/lib/x86_64-linux-gnu/libstdc++.so.6', 'BINARY'),
  ('libopenjp2.so.7', '/usr/lib/x86_64-linux-gnu/libopenjp2.so.7', 'BINARY'),
  ('libglib-2.0.so.0', '/usr/lib/x86_64-linux-gnu/libglib-2.0.so.0', 'BINARY'),
  ('libjxl.so.0.7', '/usr/lib/x86_64-linux-gnu/libjxl.so.0.7', 'BINARY'),
  ('libharfbuzz.so.0', '/usr/lib/x86_64-linux-gnu/libharfbuzz.so.0', 'BINARY'),
  ('libx264.so.164', '/usr/lib/x86_64-linux-gnu/libx264.so.164', 'BINARY'),
  ('libgpg-error.so.0',
   '/usr/lib/x86_64-linux-gnu/libgpg-error.so.0',
   'BINARY'),
  ('libgtk-3.so.0', '/usr/lib/x86_64-linux-gnu/libgtk-3.so.0', 'BINARY'),
  ('libopencv_cudafeatures2d.so.413',
   '/usr/local/lib/libopencv_cudafeatures2d.so.413',
   'BINARY'),
  ('libgstriff-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgstriff-1.0.so.0',
   'BINARY'),
  ('libX11.so.6', '/usr/lib/x86_64-linux-gnu/libX11.so.6', 'BINARY'),
  ('libnettle.so.8', '/usr/lib/x86_64-linux-gnu/libnettle.so.8', 'BINARY'),
  ('libcublas.so.12', '/usr/lib/x86_64-linux-gnu/libcublas.so.12', 'BINARY'),
  ('libatk-1.0.so.0', '/usr/lib/x86_64-linux-gnu/libatk-1.0.so.0', 'BINARY'),
  ('libcodec2.so.1.2', '/usr/lib/x86_64-linux-gnu/libcodec2.so.1.2', 'BINARY'),
  ('libva-x11.so.2', '/usr/lib/x86_64-linux-gnu/libva-x11.so.2', 'BINARY'),
  ('libopencv_structured_light.so.413',
   '/usr/local/lib/libopencv_structured_light.so.413',
   'BINARY'),
  ('libjxl_threads.so.0.7',
   '/usr/lib/x86_64-linux-gnu/libjxl_threads.so.0.7',
   'BINARY'),
  ('liborc-0.4.so.0', '/usr/lib/x86_64-linux-gnu/liborc-0.4.so.0', 'BINARY'),
  ('librist.so.4', '/usr/lib/x86_64-linux-gnu/librist.so.4', 'BINARY'),
  ('libkrb5.so.3', '/usr/lib/x86_64-linux-gnu/libkrb5.so.3', 'BINARY'),
  ('libopencv_core.so.413', '/usr/local/lib/libopencv_core.so.413', 'BINARY'),
  ('libdw.so.1', '/usr/lib/x86_64-linux-gnu/libdw.so.1', 'BINARY'),
  ('libidn2.so.0', '/usr/lib/x86_64-linux-gnu/libidn2.so.0', 'BINARY'),
  ('libopencv_optflow.so.413',
   '/usr/local/lib/libopencv_optflow.so.413',
   'BINARY'),
  ('libopencv_wechat_qrcode.so.413',
   '/usr/local/lib/libopencv_wechat_qrcode.so.413',
   'BINARY'),
  ('libgomp.so.1', '/usr/lib/x86_64-linux-gnu/libgomp.so.1', 'BINARY'),
  ('libopencv_imgcodecs.so.413',
   '/usr/local/lib/libopencv_imgcodecs.so.413',
   'BINARY'),
  ('libXdamage.so.1', '/usr/lib/x86_64-linux-gnu/libXdamage.so.1', 'BINARY'),
  ('libXinerama.so.1', '/usr/lib/x86_64-linux-gnu/libXinerama.so.1', 'BINARY'),
  ('libbrotlidec.so.1',
   '/usr/lib/x86_64-linux-gnu/libbrotlidec.so.1',
   'BINARY'),
  ('libopencv_bioinspired.so.413',
   '/usr/local/lib/libopencv_bioinspired.so.413',
   'BINARY'),
  ('libopencv_dnn.so.413', '/usr/local/lib/libopencv_dnn.so.413', 'BINARY'),
  ('libgcc_s.so.1', '/usr/lib/x86_64-linux-gnu/libgcc_s.so.1', 'BINARY'),
  ('libcairo-gobject.so.2',
   '/usr/lib/x86_64-linux-gnu/libcairo-gobject.so.2',
   'BINARY'),
  ('libcjson.so.1', '/usr/lib/x86_64-linux-gnu/libcjson.so.1', 'BINARY'),
  ('libbsd.so.0', '/usr/lib/x86_64-linux-gnu/libbsd.so.0', 'BINARY'),
  ('libnppim.so.12', '/usr/lib/x86_64-linux-gnu/libnppim.so.12', 'BINARY'),
  ('libopencv_intensity_transform.so.413',
   '/usr/local/lib/libopencv_intensity_transform.so.413',
   'BINARY'),
  ('libopencv_line_descriptor.so.413',
   '/usr/local/lib/libopencv_line_descriptor.so.413',
   'BINARY'),
  ('libxvidcore.so.4', '/usr/lib/x86_64-linux-gnu/libxvidcore.so.4', 'BINARY'),
  ('libsnappy.so.1', '/usr/lib/x86_64-linux-gnu/libsnappy.so.1', 'BINARY'),
  ('libopencv_cudawarping.so.413',
   '/usr/local/lib/libopencv_cudawarping.so.413',
   'BINARY'),
  ('libsharpyuv.so.0', '/usr/lib/x86_64-linux-gnu/libsharpyuv.so.0', 'BINARY'),
  ('libthai.so.0', '/usr/lib/x86_64-linux-gnu/libthai.so.0', 'BINARY'),
  ('libjbig.so.0', '/usr/lib/x86_64-linux-gnu/libjbig.so.0', 'BINARY'),
  ('libtheoradec.so.1',
   '/usr/lib/x86_64-linux-gnu/libtheoradec.so.1',
   'BINARY'),
  ('libzvbi.so.0', '/usr/lib/x86_64-linux-gnu/libzvbi.so.0', 'BINARY'),
  ('libopencv_hfs.so.413', '/usr/local/lib/libopencv_hfs.so.413', 'BINARY'),
  ('libspeex.so.1', '/usr/lib/x86_64-linux-gnu/libspeex.so.1', 'BINARY'),
  ('libopencv_cudaarithm.so.413',
   '/usr/local/lib/libopencv_cudaarithm.so.413',
   'BINARY'),
  ('libselinux.so.1', '/usr/lib/x86_64-linux-gnu/libselinux.so.1', 'BINARY'),
  ('libpng16.so.16', '/usr/lib/x86_64-linux-gnu/libpng16.so.16', 'BINARY'),
  ('libmd.so.0', '/usr/lib/x86_64-linux-gnu/libmd.so.0', 'BINARY'),
  ('libkeyutils.so.1', '/usr/lib/x86_64-linux-gnu/libkeyutils.so.1', 'BINARY'),
  ('libopencv_aruco.so.413', '/usr/local/lib/libopencv_aruco.so.413', 'BINARY'),
  ('libsystemd.so.0', '/usr/lib/x86_64-linux-gnu/libsystemd.so.0', 'BINARY'),
  ('libcufft.so.11', '/usr/lib/x86_64-linux-gnu/libcufft.so.11', 'BINARY'),
  ('libffi.so.8', '/usr/lib/x86_64-linux-gnu/libffi.so.8', 'BINARY'),
  ('libunistring.so.5',
   '/usr/lib/x86_64-linux-gnu/libunistring.so.5',
   'BINARY'),
  ('libopencv_xphoto.so.413',
   '/usr/local/lib/libopencv_xphoto.so.413',
   'BINARY'),
  ('libbluray.so.2', '/usr/lib/x86_64-linux-gnu/libbluray.so.2', 'BINARY'),
  ('libkrb5support.so.0',
   '/usr/lib/x86_64-linux-gnu/libkrb5support.so.0',
   'BINARY'),
  ('libXrandr.so.2', '/usr/lib/x86_64-linux-gnu/libXrandr.so.2', 'BINARY'),
  ('libopencv_video.so.413', '/usr/local/lib/libopencv_video.so.413', 'BINARY'),
  ('libbrotlicommon.so.1',
   '/usr/lib/x86_64-linux-gnu/libbrotlicommon.so.1',
   'BINARY'),
  ('libImath-3_1.so.29',
   '/usr/lib/x86_64-linux-gnu/libImath-3_1.so.29',
   'BINARY'),
  ('libXau.so.6', '/usr/lib/x86_64-linux-gnu/libXau.so.6', 'BINARY'),
  ('libopencv_text.so.413', '/usr/local/lib/libopencv_text.so.413', 'BINARY'),
  ('libopencv_cudastereo.so.413',
   '/usr/local/lib/libopencv_cudastereo.so.413',
   'BINARY'),
  ('libopencv_cudalegacy.so.413',
   '/usr/local/lib/libopencv_cudalegacy.so.413',
   'BINARY'),
  ('libgstvideo-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgstvideo-1.0.so.0',
   'BINARY'),
  ('libtasn1.so.6', '/usr/lib/x86_64-linux-gnu/libtasn1.so.6', 'BINARY'),
  ('libgcrypt.so.20', '/usr/lib/x86_64-linux-gnu/libgcrypt.so.20', 'BINARY'),
  ('libssh-gcrypt.so.4',
   '/usr/lib/x86_64-linux-gnu/libssh-gcrypt.so.4',
   'BINARY'),
  ('libgio-2.0.so.0', '/usr/lib/x86_64-linux-gnu/libgio-2.0.so.0', 'BINARY'),
  ('libopencv_cudaimgproc.so.413',
   '/usr/local/lib/libopencv_cudaimgproc.so.413',
   'BINARY'),
  ('libopencv_shape.so.413', '/usr/local/lib/libopencv_shape.so.413', 'BINARY'),
  ('libgnutls.so.30', '/usr/lib/x86_64-linux-gnu/libgnutls.so.30', 'BINARY'),
  ('libgraphite2.so.3',
   '/usr/lib/x86_64-linux-gnu/libgraphite2.so.3',
   'BINARY'),
  ('libgstpbutils-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgstpbutils-1.0.so.0',
   'BINARY'),
  ('libopencv_calib3d.so.413',
   '/usr/local/lib/libopencv_calib3d.so.413',
   'BINARY'),
  ('libOpenEXR-3_1.so.30',
   '/usr/lib/x86_64-linux-gnu/libOpenEXR-3_1.so.30',
   'BINARY'),
  ('libreadline.so.8', '/usr/lib/x86_64-linux-gnu/libreadline.so.8', 'BINARY'),
  ('libtinfo.so.6', '/usr/lib/x86_64-linux-gnu/libtinfo.so.6', 'BINARY'),
  ('libncursesw.so.6', '/usr/lib/x86_64-linux-gnu/libncursesw.so.6', 'BINARY'),
  ('numpy.libs/libquadmath-96973f99.so.0.0.0',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy.libs/libquadmath-96973f99.so.0.0.0',
   'BINARY'),
  ('numpy.libs/libopenblas64_p-r0-0cf96a72.3.23.dev.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy.libs/libopenblas64_p-r0-0cf96a72.3.23.dev.so',
   'BINARY'),
  ('numpy.libs/libgfortran-040039e1.so.5.0.0',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy.libs/libgfortran-040039e1.so.5.0.0',
   'BINARY'),
  ('pillow.libs/libXau-154567c4.so.6.0.0',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libXau-154567c4.so.6.0.0',
   'BINARY'),
  ('pillow.libs/libxcb-64009ff3.so.1.1.0',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libxcb-64009ff3.so.1.1.0',
   'BINARY'),
  ('pillow.libs/liblzma-64b7ab39.so.5.8.1',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/liblzma-64b7ab39.so.5.8.1',
   'BINARY'),
  ('pillow.libs/libjpeg-8a13c6e0.so.62.4.0',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libjpeg-8a13c6e0.so.62.4.0',
   'BINARY'),
  ('pillow.libs/libtiff-13a02c81.so.6.1.0',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libtiff-13a02c81.so.6.1.0',
   'BINARY'),
  ('pillow.libs/libopenjp2-56811f71.so.2.5.3',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libopenjp2-56811f71.so.2.5.3',
   'BINARY'),
  ('pillow.libs/libwebpmux-6f2b1ad9.so.3.1.1',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libwebpmux-6f2b1ad9.so.3.1.1',
   'BINARY'),
  ('pillow.libs/libwebp-5f0275c0.so.7.1.10',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libwebp-5f0275c0.so.7.1.10',
   'BINARY'),
  ('pillow.libs/libsharpyuv-60a7c00b.so.0.1.1',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libsharpyuv-60a7c00b.so.0.1.1',
   'BINARY'),
  ('pillow.libs/libwebpdemux-efaed568.so.2.0.16',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libwebpdemux-efaed568.so.2.0.16',
   'BINARY'),
  ('pillow.libs/libavif-01e67780.so.16.3.0',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libavif-01e67780.so.16.3.0',
   'BINARY'),
  ('pillow.libs/liblcms2-cc10e42f.so.2.0.17',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/liblcms2-cc10e42f.so.2.0.17',
   'BINARY'),
  ('libgdbm.so.6', '/usr/lib/x86_64-linux-gnu/libgdbm.so.6', 'BINARY'),
  ('libdb-5.3.so', '/usr/lib/x86_64-linux-gnu/libdb-5.3.so', 'BINARY'),
  ('cv2/Error/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/Error/__init__.pyi',
   'DATA'),
  ('cv2/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/__init__.pyi',
   'DATA'),
  ('cv2/aruco/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/aruco/__init__.pyi',
   'DATA'),
  ('cv2/barcode/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/barcode/__init__.pyi',
   'DATA'),
  ('cv2/bgsegm/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/bgsegm/__init__.pyi',
   'DATA'),
  ('cv2/bioinspired/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/bioinspired/__init__.pyi',
   'DATA'),
  ('cv2/ccm/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ccm/__init__.pyi',
   'DATA'),
  ('cv2/colored_kinfu/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/colored_kinfu/__init__.pyi',
   'DATA'),
  ('cv2/cuda/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/cuda/__init__.pyi',
   'DATA'),
  ('cv2/cudacodec/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/cudacodec/__init__.pyi',
   'DATA'),
  ('cv2/datasets/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/datasets/__init__.pyi',
   'DATA'),
  ('cv2/detail/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/detail/__init__.pyi',
   'DATA'),
  ('cv2/dnn/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/dnn/__init__.pyi',
   'DATA'),
  ('cv2/dnn_superres/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/dnn_superres/__init__.pyi',
   'DATA'),
  ('cv2/dpm/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/dpm/__init__.pyi',
   'DATA'),
  ('cv2/dynafu/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/dynafu/__init__.pyi',
   'DATA'),
  ('cv2/face/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/face/__init__.pyi',
   'DATA'),
  ('cv2/fisheye/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/fisheye/__init__.pyi',
   'DATA'),
  ('cv2/flann/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/flann/__init__.pyi',
   'DATA'),
  ('cv2/freetype/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/freetype/__init__.pyi',
   'DATA'),
  ('cv2/ft/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ft/__init__.pyi',
   'DATA'),
  ('cv2/gapi/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/__init__.pyi',
   'DATA'),
  ('cv2/gapi/core/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/core/__init__.pyi',
   'DATA'),
  ('cv2/gapi/core/cpu/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/core/cpu/__init__.pyi',
   'DATA'),
  ('cv2/gapi/core/fluid/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/core/fluid/__init__.pyi',
   'DATA'),
  ('cv2/gapi/core/ocl/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/core/ocl/__init__.pyi',
   'DATA'),
  ('cv2/gapi/ie/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/ie/__init__.pyi',
   'DATA'),
  ('cv2/gapi/ie/detail/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/ie/detail/__init__.pyi',
   'DATA'),
  ('cv2/gapi/imgproc/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/imgproc/__init__.pyi',
   'DATA'),
  ('cv2/gapi/imgproc/fluid/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/imgproc/fluid/__init__.pyi',
   'DATA'),
  ('cv2/gapi/oak/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/oak/__init__.pyi',
   'DATA'),
  ('cv2/gapi/onnx/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/onnx/__init__.pyi',
   'DATA'),
  ('cv2/gapi/onnx/ep/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/onnx/ep/__init__.pyi',
   'DATA'),
  ('cv2/gapi/ot/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/ot/__init__.pyi',
   'DATA'),
  ('cv2/gapi/ot/cpu/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/ot/cpu/__init__.pyi',
   'DATA'),
  ('cv2/gapi/ov/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/ov/__init__.pyi',
   'DATA'),
  ('cv2/gapi/own/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/own/__init__.pyi',
   'DATA'),
  ('cv2/gapi/own/detail/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/own/detail/__init__.pyi',
   'DATA'),
  ('cv2/gapi/render/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/render/__init__.pyi',
   'DATA'),
  ('cv2/gapi/render/ocv/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/render/ocv/__init__.pyi',
   'DATA'),
  ('cv2/gapi/streaming/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/streaming/__init__.pyi',
   'DATA'),
  ('cv2/gapi/video/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/video/__init__.pyi',
   'DATA'),
  ('cv2/gapi/wip/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/wip/__init__.pyi',
   'DATA'),
  ('cv2/gapi/wip/draw/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/wip/draw/__init__.pyi',
   'DATA'),
  ('cv2/gapi/wip/gst/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/wip/gst/__init__.pyi',
   'DATA'),
  ('cv2/gapi/wip/onevpl/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/wip/onevpl/__init__.pyi',
   'DATA'),
  ('cv2/hfs/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/hfs/__init__.pyi',
   'DATA'),
  ('cv2/img_hash/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/img_hash/__init__.pyi',
   'DATA'),
  ('cv2/intensity_transform/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/intensity_transform/__init__.pyi',
   'DATA'),
  ('cv2/ipp/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ipp/__init__.pyi',
   'DATA'),
  ('cv2/kinfu/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/kinfu/__init__.pyi',
   'DATA'),
  ('cv2/kinfu/detail/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/kinfu/detail/__init__.pyi',
   'DATA'),
  ('cv2/large_kinfu/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/large_kinfu/__init__.pyi',
   'DATA'),
  ('cv2/legacy/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/legacy/__init__.pyi',
   'DATA'),
  ('cv2/line_descriptor/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/line_descriptor/__init__.pyi',
   'DATA'),
  ('cv2/linemod/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/linemod/__init__.pyi',
   'DATA'),
  ('cv2/mcc/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/mcc/__init__.pyi',
   'DATA'),
  ('cv2/ml/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ml/__init__.pyi',
   'DATA'),
  ('cv2/motempl/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/motempl/__init__.pyi',
   'DATA'),
  ('cv2/multicalib/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/multicalib/__init__.pyi',
   'DATA'),
  ('cv2/ocl/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ocl/__init__.pyi',
   'DATA'),
  ('cv2/ogl/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ogl/__init__.pyi',
   'DATA'),
  ('cv2/omnidir/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/omnidir/__init__.pyi',
   'DATA'),
  ('cv2/optflow/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/optflow/__init__.pyi',
   'DATA'),
  ('cv2/parallel/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/parallel/__init__.pyi',
   'DATA'),
  ('cv2/phase_unwrapping/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/phase_unwrapping/__init__.pyi',
   'DATA'),
  ('cv2/plot/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/plot/__init__.pyi',
   'DATA'),
  ('cv2/ppf_match_3d/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ppf_match_3d/__init__.pyi',
   'DATA'),
  ('cv2/py.typed',
   '/usr/local/lib/python3.12/dist-packages/cv2/py.typed',
   'DATA'),
  ('cv2/quality/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/quality/__init__.pyi',
   'DATA'),
  ('cv2/rapid/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/rapid/__init__.pyi',
   'DATA'),
  ('cv2/reg/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/reg/__init__.pyi',
   'DATA'),
  ('cv2/rgbd/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/rgbd/__init__.pyi',
   'DATA'),
  ('cv2/saliency/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/saliency/__init__.pyi',
   'DATA'),
  ('cv2/samples/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/samples/__init__.pyi',
   'DATA'),
  ('cv2/segmentation/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/segmentation/__init__.pyi',
   'DATA'),
  ('cv2/signal/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/signal/__init__.pyi',
   'DATA'),
  ('cv2/stereo/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/stereo/__init__.pyi',
   'DATA'),
  ('cv2/structured_light/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/structured_light/__init__.pyi',
   'DATA'),
  ('cv2/text/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/text/__init__.pyi',
   'DATA'),
  ('cv2/utils/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/utils/__init__.pyi',
   'DATA'),
  ('cv2/utils/fs/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/utils/fs/__init__.pyi',
   'DATA'),
  ('cv2/utils/nested/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/utils/nested/__init__.pyi',
   'DATA'),
  ('cv2/videoio_registry/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/videoio_registry/__init__.pyi',
   'DATA'),
  ('cv2/videostab/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/videostab/__init__.pyi',
   'DATA'),
  ('cv2/wechat_qrcode/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/wechat_qrcode/__init__.pyi',
   'DATA'),
  ('cv2/xfeatures2d/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/xfeatures2d/__init__.pyi',
   'DATA'),
  ('cv2/ximgproc/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ximgproc/__init__.pyi',
   'DATA'),
  ('cv2/ximgproc/segmentation/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ximgproc/segmentation/__init__.pyi',
   'DATA'),
  ('cv2/xphoto/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/xphoto/__init__.pyi',
   'DATA'),
  ('flask/py.typed',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/py.typed',
   'DATA'),
  ('routes/__init__.py',
   '/home/<USER>/projects/homework/backend/routes/__init__.py',
   'DATA'),
  ('routes/__pycache__/__init__.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/__init__.cpython-312.pyc',
   'DATA'),
  ('routes/__pycache__/download.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/download.cpython-312.pyc',
   'DATA'),
  ('routes/__pycache__/preview.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/preview.cpython-312.pyc',
   'DATA'),
  ('routes/__pycache__/process.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/process.cpython-312.pyc',
   'DATA'),
  ('routes/__pycache__/system.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/system.cpython-312.pyc',
   'DATA'),
  ('routes/__pycache__/thumbnail.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/thumbnail.cpython-312.pyc',
   'DATA'),
  ('routes/__pycache__/upload.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/upload.cpython-312.pyc',
   'DATA'),
  ('routes/download.py',
   '/home/<USER>/projects/homework/backend/routes/download.py',
   'DATA'),
  ('routes/preview.py',
   '/home/<USER>/projects/homework/backend/routes/preview.py',
   'DATA'),
  ('routes/process.py',
   '/home/<USER>/projects/homework/backend/routes/process.py',
   'DATA'),
  ('routes/system.py',
   '/home/<USER>/projects/homework/backend/routes/system.py',
   'DATA'),
  ('routes/thumbnail.py',
   '/home/<USER>/projects/homework/backend/routes/thumbnail.py',
   'DATA'),
  ('routes/upload.py',
   '/home/<USER>/projects/homework/backend/routes/upload.py',
   'DATA'),
  ('tasks/__init__.py',
   '/home/<USER>/projects/homework/backend/tasks/__init__.py',
   'DATA'),
  ('tasks/__pycache__/__init__.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/__init__.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/advanced_image_processing.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/advanced_image_processing.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/beauty_processing.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/beauty_processing.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/ffmpeg_video_processor.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/ffmpeg_video_processor.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/image_filters.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/image_filters.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/optimized_video_processor.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/optimized_video_processor.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/parallel_video_processor.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/parallel_video_processor.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/pure_opencv_ffmpeg_processor.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/pure_opencv_ffmpeg_processor.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/truly_optimized_video_processor.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/truly_optimized_video_processor.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/video_processor.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/video_processor.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/video_tasks.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/video_tasks.cpython-39.pyc',
   'DATA'),
  ('tasks/beauty_processing.py',
   '/home/<USER>/projects/homework/backend/tasks/beauty_processing.py',
   'DATA'),
  ('tasks/image_edge_detection.py',
   '/home/<USER>/projects/homework/backend/tasks/image_edge_detection.py',
   'DATA'),
  ('tasks/image_fusion.py',
   '/home/<USER>/projects/homework/backend/tasks/image_fusion.py',
   'DATA'),
  ('tasks/image_gamma_correction.py',
   '/home/<USER>/projects/homework/backend/tasks/image_gamma_correction.py',
   'DATA'),
  ('tasks/image_grayscale.py',
   '/home/<USER>/projects/homework/backend/tasks/image_grayscale.py',
   'DATA'),
  ('tasks/image_sharpen.py',
   '/home/<USER>/projects/homework/backend/tasks/image_sharpen.py',
   'DATA'),
  ('tasks/image_stitching.py',
   '/home/<USER>/projects/homework/backend/tasks/image_stitching.py',
   'DATA'),
  ('tasks/opencv_ffmpeg_processor.py',
   '/home/<USER>/projects/homework/backend/tasks/opencv_ffmpeg_processor.py',
   'DATA'),
  ('tasks/texture_transfer.py',
   '/home/<USER>/projects/homework/backend/tasks/texture_transfer.py',
   'DATA'),
  ('tasks/video_binary.py',
   '/home/<USER>/projects/homework/backend/tasks/video_binary.py',
   'DATA'),
  ('tasks/video_blur.py',
   '/home/<USER>/projects/homework/backend/tasks/video_blur.py',
   'DATA'),
  ('tasks/video_edge_detection.py',
   '/home/<USER>/projects/homework/backend/tasks/video_edge_detection.py',
   'DATA'),
  ('tasks/video_extract_frame.py',
   '/home/<USER>/projects/homework/backend/tasks/video_extract_frame.py',
   'DATA'),
  ('tasks/video_grayscale.py',
   '/home/<USER>/projects/homework/backend/tasks/video_grayscale.py',
   'DATA'),
  ('tasks/video_processing/__init__.py',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/__init__.py',
   'DATA'),
  ('tasks/video_processing/__pycache__/__init__.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/__pycache__/__init__.cpython-312.pyc',
   'DATA'),
  ('tasks/video_processing/__pycache__/ffmpeg_processor.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/__pycache__/ffmpeg_processor.cpython-312.pyc',
   'DATA'),
  ('tasks/video_processing/__pycache__/opencv_processors.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/__pycache__/opencv_processors.cpython-312.pyc',
   'DATA'),
  ('tasks/video_processing/__pycache__/performance_config.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/__pycache__/performance_config.cpython-312.pyc',
   'DATA'),
  ('tasks/video_processing/ffmpeg_processor.py',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/ffmpeg_processor.py',
   'DATA'),
  ('tasks/video_processing/opencv_processors.py',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/opencv_processors.py',
   'DATA'),
  ('tasks/video_processing/performance_config.py',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/performance_config.py',
   'DATA'),
  ('tasks/video_resize.py',
   '/home/<USER>/projects/homework/backend/tasks/video_resize.py',
   'DATA'),
  ('tasks/video_tasks.py',
   '/home/<USER>/projects/homework/backend/tasks/video_tasks.py',
   'DATA'),
  ('tasks/video_thumbnail.py',
   '/home/<USER>/projects/homework/backend/tasks/video_thumbnail.py',
   'DATA'),
  ('tasks/video_transform.py',
   '/home/<USER>/projects/homework/backend/tasks/video_transform.py',
   'DATA'),
  ('utils/__init__.py',
   '/home/<USER>/projects/homework/backend/utils/__init__.py',
   'DATA'),
  ('utils/__pycache__/__init__.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/utils/__pycache__/__init__.cpython-312.pyc',
   'DATA'),
  ('utils/__pycache__/cache_manager.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/utils/__pycache__/cache_manager.cpython-312.pyc',
   'DATA'),
  ('utils/__pycache__/validation.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/utils/__pycache__/validation.cpython-312.pyc',
   'DATA'),
  ('utils/cache_manager.py',
   '/home/<USER>/projects/homework/backend/utils/cache_manager.py',
   'DATA'),
  ('utils/validation.py',
   '/home/<USER>/projects/homework/backend/utils/validation.py',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   'DATA'),
  ('setuptools/_vendor/jaraco/text/Lorem ipsum.txt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/Lorem '
   'ipsum.txt',
   'DATA'),
  ('imageio_ffmpeg/binaries/README.md',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio_ffmpeg/binaries/README.md',
   'DATA'),
  ('certifi/py.typed',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/certifi/py.typed',
   'DATA'),
  ('certifi/cacert.pem',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/certifi/cacert.pem',
   'DATA'),
  ('dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('cv2/config.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/config.py',
   'DATA'),
  ('cv2/config-3.12.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/config-3.12.py',
   'DATA'),
  ('cv2/load_config_py3.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/load_config_py3.py',
   'DATA'),
  ('prompt_toolkit-3.0.51.dist-info/top_level.txt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit-3.0.51.dist-info/top_level.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info/WHEEL',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug-3.1.3.dist-info/WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info/RECORD',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/itsdangerous-2.2.0.dist-info/RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info/METADATA',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/itsdangerous-2.2.0.dist-info/METADATA',
   'DATA'),
  ('click-8.2.1.dist-info/METADATA',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click-8.2.1.dist-info/METADATA',
   'DATA'),
  ('flask-2.3.3.dist-info/entry_points.txt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask-2.3.3.dist-info/entry_points.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info/METADATA',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug-3.1.3.dist-info/METADATA',
   'DATA'),
  ('flask-2.3.3.dist-info/LICENSE.rst',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask-2.3.3.dist-info/LICENSE.rst',
   'DATA'),
  ('prompt_toolkit-3.0.51.dist-info/WHEEL',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit-3.0.51.dist-info/WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info/LICENSE.txt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/itsdangerous-2.2.0.dist-info/LICENSE.txt',
   'DATA'),
  ('prompt_toolkit-3.0.51.dist-info/licenses/LICENSE',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit-3.0.51.dist-info/licenses/LICENSE',
   'DATA'),
  ('werkzeug-3.1.3.dist-info/INSTALLER',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug-3.1.3.dist-info/INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info/RECORD',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click-8.2.1.dist-info/RECORD',
   'DATA'),
  ('prompt_toolkit-3.0.51.dist-info/METADATA',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit-3.0.51.dist-info/METADATA',
   'DATA'),
  ('flask-2.3.3.dist-info/INSTALLER',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask-2.3.3.dist-info/INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info/INSTALLER',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/itsdangerous-2.2.0.dist-info/INSTALLER',
   'DATA'),
  ('flask-2.3.3.dist-info/REQUESTED',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask-2.3.3.dist-info/REQUESTED',
   'DATA'),
  ('prompt_toolkit-3.0.51.dist-info/RECORD',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit-3.0.51.dist-info/RECORD',
   'DATA'),
  ('flask-2.3.3.dist-info/RECORD',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask-2.3.3.dist-info/RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/LICENSE.txt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/INSTALLER',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info/WHEEL',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click-8.2.1.dist-info/WHEEL',
   'DATA'),
  ('prompt_toolkit-3.0.51.dist-info/licenses/AUTHORS.rst',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit-3.0.51.dist-info/licenses/AUTHORS.rst',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/WHEEL',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/RECORD',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/RECORD',
   'DATA'),
  ('click-8.2.1.dist-info/INSTALLER',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click-8.2.1.dist-info/INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info/WHEEL',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/itsdangerous-2.2.0.dist-info/WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info/LICENSE.txt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug-3.1.3.dist-info/LICENSE.txt',
   'DATA'),
  ('flask-2.3.3.dist-info/WHEEL',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask-2.3.3.dist-info/WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info/licenses/LICENSE.txt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click-8.2.1.dist-info/licenses/LICENSE.txt',
   'DATA'),
  ('prompt_toolkit-3.0.51.dist-info/INSTALLER',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit-3.0.51.dist-info/INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info/RECORD',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug-3.1.3.dist-info/RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/METADATA',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/top_level.txt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/top_level.txt',
   'DATA'),
  ('flask-2.3.3.dist-info/METADATA',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask-2.3.3.dist-info/METADATA',
   'DATA'),
  ('cv2/__init__.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/__init__.py',
   'DATA'),
  ('cv2/utils/__init__.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/utils/__init__.py',
   'DATA'),
  ('cv2/typing/__init__.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/typing/__init__.py',
   'DATA'),
  ('cv2/misc/version.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/misc/version.py',
   'DATA'),
  ('cv2/misc/__init__.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/misc/__init__.py',
   'DATA'),
  ('cv2/mat_wrapper/__init__.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/mat_wrapper/__init__.py',
   'DATA'),
  ('cv2/gapi/__init__.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/__init__.py',
   'DATA'),
  ('base_library.zip',
   '/home/<USER>/projects/homework/backend/build/desktop_app/base_library.zip',
   'DATA'),
  ('libquadmath-96973f99.so.0.0.0',
   'numpy.libs/libquadmath-96973f99.so.0.0.0',
   'SYMLINK'),
  ('libopenblas64_p-r0-0cf96a72.3.23.dev.so',
   'numpy.libs/libopenblas64_p-r0-0cf96a72.3.23.dev.so',
   'SYMLINK'),
  ('libgfortran-040039e1.so.5.0.0',
   'numpy.libs/libgfortran-040039e1.so.5.0.0',
   'SYMLINK'),
  ('libXau-154567c4.so.6.0.0',
   'pillow.libs/libXau-154567c4.so.6.0.0',
   'SYMLINK'),
  ('libxcb-64009ff3.so.1.1.0',
   'pillow.libs/libxcb-64009ff3.so.1.1.0',
   'SYMLINK'),
  ('liblzma-64b7ab39.so.5.8.1',
   'pillow.libs/liblzma-64b7ab39.so.5.8.1',
   'SYMLINK'),
  ('libjpeg-8a13c6e0.so.62.4.0',
   'pillow.libs/libjpeg-8a13c6e0.so.62.4.0',
   'SYMLINK'),
  ('libtiff-13a02c81.so.6.1.0',
   'pillow.libs/libtiff-13a02c81.so.6.1.0',
   'SYMLINK'),
  ('libopenjp2-56811f71.so.2.5.3',
   'pillow.libs/libopenjp2-56811f71.so.2.5.3',
   'SYMLINK'),
  ('libwebpmux-6f2b1ad9.so.3.1.1',
   'pillow.libs/libwebpmux-6f2b1ad9.so.3.1.1',
   'SYMLINK'),
  ('libwebp-5f0275c0.so.7.1.10',
   'pillow.libs/libwebp-5f0275c0.so.7.1.10',
   'SYMLINK'),
  ('libsharpyuv-60a7c00b.so.0.1.1',
   'pillow.libs/libsharpyuv-60a7c00b.so.0.1.1',
   'SYMLINK'),
  ('libwebpdemux-efaed568.so.2.0.16',
   'pillow.libs/libwebpdemux-efaed568.so.2.0.16',
   'SYMLINK'),
  ('libavif-01e67780.so.16.3.0',
   'pillow.libs/libavif-01e67780.so.16.3.0',
   'SYMLINK'),
  ('liblcms2-cc10e42f.so.2.0.17',
   'pillow.libs/liblcms2-cc10e42f.so.2.0.17',
   'SYMLINK')],
 [],
 False,
 False,
 1753940508,
 [('run',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PyInstaller/bootloader/Linux-64bit-intel/run',
   'EXECUTABLE')],
 '/usr/lib/x86_64-linux-gnu/libpython3.12.so')
