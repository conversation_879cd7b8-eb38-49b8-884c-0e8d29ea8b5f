(['/home/<USER>/projects/homework/backend/desktop_app_sync.py'],
 ['/home/<USER>/projects/homework/backend',
  '/home/<USER>/projects/homework/backend'],
 ['flask',
  'flask_cors',
  'werkzeug',
  'jinja2',
  'markupsafe',
  'itsdangerous',
  'click',
  'celery',
  'celery.app',
  'celery.worker',
  'celery.backends',
  'celery.backends.cache',
  'celery.fixups',
  'celery.fixups.django',
  'celery.loaders',
  'celery.loaders.app',
  'celery.security',
  'celery.concurrency',
  'celery.concurrency.threads',
  'celery.concurrency.solo',
  'celery.concurrency.base',
  'kombu',
  'kombu.transport',
  'kombu.transport.memory',
  'kombu.serialization',
  'billiard',
  'billiard.pool',
  'cv2',
  'numpy',
  'PIL',
  'PIL.Image',
  'PIL.ImageFilter',
  'PIL.ImageEnhance',
  'moviepy',
  'moviepy.editor',
  'moviepy.video.io.VideoFileClip',
  'moviepy.video.fx',
  'moviepy.audio.fx',
  'psutil',
  'pathlib',
  'multiprocessing',
  'threading',
  'socket',
  'json',
  'logging',
  'time',
  'os',
  'sys',
  'config',
  'routes.upload',
  'routes.preview',
  'routes.download',
  'routes.process',
  'routes.system',
  'routes.thumbnail',
  'tasks',
  'utils.validation',
  'utils.cache_manager',
  'tasks.image_sharpen',
  'tasks.image_gamma_correction',
  'tasks.image_grayscale',
  'tasks.image_edge_detection',
  'tasks.image_fusion',
  'tasks.image_stitching',
  'tasks.texture_transfer',
  'tasks.beauty_processing',
  'tasks.video_tasks',
  'tasks.video_grayscale',
  'tasks.video_resize',
  'tasks.video_binary',
  'tasks.video_blur',
  'tasks.video_edge_detection',
  'tasks.video_transform',
  'tasks.video_extract_frame',
  'tasks.video_thumbnail'],
 [('/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/_pyinstaller',
   0),
  ('/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['tkinter',
  'matplotlib',
  'scipy',
  'pandas',
  'jupyter',
  'IPython',
  'notebook',
  'pytest',
  'unittest',
  '__main__'],
 [],
 False,
 {},
 0,
 [],
 [('cv2/Error/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/Error/__init__.pyi',
   'DATA'),
  ('cv2/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/__init__.pyi',
   'DATA'),
  ('cv2/aruco/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/aruco/__init__.pyi',
   'DATA'),
  ('cv2/barcode/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/barcode/__init__.pyi',
   'DATA'),
  ('cv2/bgsegm/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/bgsegm/__init__.pyi',
   'DATA'),
  ('cv2/bioinspired/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/bioinspired/__init__.pyi',
   'DATA'),
  ('cv2/ccm/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ccm/__init__.pyi',
   'DATA'),
  ('cv2/colored_kinfu/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/colored_kinfu/__init__.pyi',
   'DATA'),
  ('cv2/cuda/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/cuda/__init__.pyi',
   'DATA'),
  ('cv2/cudacodec/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/cudacodec/__init__.pyi',
   'DATA'),
  ('cv2/datasets/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/datasets/__init__.pyi',
   'DATA'),
  ('cv2/detail/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/detail/__init__.pyi',
   'DATA'),
  ('cv2/dnn/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/dnn/__init__.pyi',
   'DATA'),
  ('cv2/dnn_superres/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/dnn_superres/__init__.pyi',
   'DATA'),
  ('cv2/dpm/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/dpm/__init__.pyi',
   'DATA'),
  ('cv2/dynafu/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/dynafu/__init__.pyi',
   'DATA'),
  ('cv2/face/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/face/__init__.pyi',
   'DATA'),
  ('cv2/fisheye/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/fisheye/__init__.pyi',
   'DATA'),
  ('cv2/flann/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/flann/__init__.pyi',
   'DATA'),
  ('cv2/freetype/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/freetype/__init__.pyi',
   'DATA'),
  ('cv2/ft/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ft/__init__.pyi',
   'DATA'),
  ('cv2/gapi/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/__init__.pyi',
   'DATA'),
  ('cv2/gapi/core/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/core/__init__.pyi',
   'DATA'),
  ('cv2/gapi/core/cpu/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/core/cpu/__init__.pyi',
   'DATA'),
  ('cv2/gapi/core/fluid/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/core/fluid/__init__.pyi',
   'DATA'),
  ('cv2/gapi/core/ocl/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/core/ocl/__init__.pyi',
   'DATA'),
  ('cv2/gapi/ie/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/ie/__init__.pyi',
   'DATA'),
  ('cv2/gapi/ie/detail/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/ie/detail/__init__.pyi',
   'DATA'),
  ('cv2/gapi/imgproc/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/imgproc/__init__.pyi',
   'DATA'),
  ('cv2/gapi/imgproc/fluid/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/imgproc/fluid/__init__.pyi',
   'DATA'),
  ('cv2/gapi/oak/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/oak/__init__.pyi',
   'DATA'),
  ('cv2/gapi/onnx/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/onnx/__init__.pyi',
   'DATA'),
  ('cv2/gapi/onnx/ep/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/onnx/ep/__init__.pyi',
   'DATA'),
  ('cv2/gapi/ot/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/ot/__init__.pyi',
   'DATA'),
  ('cv2/gapi/ot/cpu/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/ot/cpu/__init__.pyi',
   'DATA'),
  ('cv2/gapi/ov/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/ov/__init__.pyi',
   'DATA'),
  ('cv2/gapi/own/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/own/__init__.pyi',
   'DATA'),
  ('cv2/gapi/own/detail/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/own/detail/__init__.pyi',
   'DATA'),
  ('cv2/gapi/render/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/render/__init__.pyi',
   'DATA'),
  ('cv2/gapi/render/ocv/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/render/ocv/__init__.pyi',
   'DATA'),
  ('cv2/gapi/streaming/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/streaming/__init__.pyi',
   'DATA'),
  ('cv2/gapi/video/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/video/__init__.pyi',
   'DATA'),
  ('cv2/gapi/wip/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/wip/__init__.pyi',
   'DATA'),
  ('cv2/gapi/wip/draw/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/wip/draw/__init__.pyi',
   'DATA'),
  ('cv2/gapi/wip/gst/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/wip/gst/__init__.pyi',
   'DATA'),
  ('cv2/gapi/wip/onevpl/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/wip/onevpl/__init__.pyi',
   'DATA'),
  ('cv2/hfs/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/hfs/__init__.pyi',
   'DATA'),
  ('cv2/img_hash/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/img_hash/__init__.pyi',
   'DATA'),
  ('cv2/intensity_transform/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/intensity_transform/__init__.pyi',
   'DATA'),
  ('cv2/ipp/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ipp/__init__.pyi',
   'DATA'),
  ('cv2/kinfu/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/kinfu/__init__.pyi',
   'DATA'),
  ('cv2/kinfu/detail/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/kinfu/detail/__init__.pyi',
   'DATA'),
  ('cv2/large_kinfu/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/large_kinfu/__init__.pyi',
   'DATA'),
  ('cv2/legacy/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/legacy/__init__.pyi',
   'DATA'),
  ('cv2/line_descriptor/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/line_descriptor/__init__.pyi',
   'DATA'),
  ('cv2/linemod/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/linemod/__init__.pyi',
   'DATA'),
  ('cv2/mcc/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/mcc/__init__.pyi',
   'DATA'),
  ('cv2/ml/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ml/__init__.pyi',
   'DATA'),
  ('cv2/motempl/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/motempl/__init__.pyi',
   'DATA'),
  ('cv2/multicalib/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/multicalib/__init__.pyi',
   'DATA'),
  ('cv2/ocl/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ocl/__init__.pyi',
   'DATA'),
  ('cv2/ogl/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ogl/__init__.pyi',
   'DATA'),
  ('cv2/omnidir/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/omnidir/__init__.pyi',
   'DATA'),
  ('cv2/optflow/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/optflow/__init__.pyi',
   'DATA'),
  ('cv2/parallel/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/parallel/__init__.pyi',
   'DATA'),
  ('cv2/phase_unwrapping/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/phase_unwrapping/__init__.pyi',
   'DATA'),
  ('cv2/plot/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/plot/__init__.pyi',
   'DATA'),
  ('cv2/ppf_match_3d/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ppf_match_3d/__init__.pyi',
   'DATA'),
  ('cv2/py.typed',
   '/usr/local/lib/python3.12/dist-packages/cv2/py.typed',
   'DATA'),
  ('cv2/quality/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/quality/__init__.pyi',
   'DATA'),
  ('cv2/rapid/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/rapid/__init__.pyi',
   'DATA'),
  ('cv2/reg/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/reg/__init__.pyi',
   'DATA'),
  ('cv2/rgbd/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/rgbd/__init__.pyi',
   'DATA'),
  ('cv2/saliency/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/saliency/__init__.pyi',
   'DATA'),
  ('cv2/samples/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/samples/__init__.pyi',
   'DATA'),
  ('cv2/segmentation/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/segmentation/__init__.pyi',
   'DATA'),
  ('cv2/signal/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/signal/__init__.pyi',
   'DATA'),
  ('cv2/stereo/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/stereo/__init__.pyi',
   'DATA'),
  ('cv2/structured_light/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/structured_light/__init__.pyi',
   'DATA'),
  ('cv2/text/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/text/__init__.pyi',
   'DATA'),
  ('cv2/utils/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/utils/__init__.pyi',
   'DATA'),
  ('cv2/utils/fs/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/utils/fs/__init__.pyi',
   'DATA'),
  ('cv2/utils/nested/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/utils/nested/__init__.pyi',
   'DATA'),
  ('cv2/videoio_registry/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/videoio_registry/__init__.pyi',
   'DATA'),
  ('cv2/videostab/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/videostab/__init__.pyi',
   'DATA'),
  ('cv2/wechat_qrcode/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/wechat_qrcode/__init__.pyi',
   'DATA'),
  ('cv2/xfeatures2d/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/xfeatures2d/__init__.pyi',
   'DATA'),
  ('cv2/ximgproc/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ximgproc/__init__.pyi',
   'DATA'),
  ('cv2/ximgproc/segmentation/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ximgproc/segmentation/__init__.pyi',
   'DATA'),
  ('cv2/xphoto/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/xphoto/__init__.pyi',
   'DATA'),
  ('flask/py.typed',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/py.typed',
   'DATA'),
  ('routes/__init__.py',
   '/home/<USER>/projects/homework/backend/routes/__init__.py',
   'DATA'),
  ('routes/__pycache__/__init__.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/__init__.cpython-312.pyc',
   'DATA'),
  ('routes/__pycache__/download.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/download.cpython-312.pyc',
   'DATA'),
  ('routes/__pycache__/preview.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/preview.cpython-312.pyc',
   'DATA'),
  ('routes/__pycache__/process.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/process.cpython-312.pyc',
   'DATA'),
  ('routes/__pycache__/system.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/system.cpython-312.pyc',
   'DATA'),
  ('routes/__pycache__/thumbnail.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/thumbnail.cpython-312.pyc',
   'DATA'),
  ('routes/__pycache__/upload.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/upload.cpython-312.pyc',
   'DATA'),
  ('routes/download.py',
   '/home/<USER>/projects/homework/backend/routes/download.py',
   'DATA'),
  ('routes/preview.py',
   '/home/<USER>/projects/homework/backend/routes/preview.py',
   'DATA'),
  ('routes/process.py',
   '/home/<USER>/projects/homework/backend/routes/process.py',
   'DATA'),
  ('routes/system.py',
   '/home/<USER>/projects/homework/backend/routes/system.py',
   'DATA'),
  ('routes/thumbnail.py',
   '/home/<USER>/projects/homework/backend/routes/thumbnail.py',
   'DATA'),
  ('routes/upload.py',
   '/home/<USER>/projects/homework/backend/routes/upload.py',
   'DATA'),
  ('tasks/__init__.py',
   '/home/<USER>/projects/homework/backend/tasks/__init__.py',
   'DATA'),
  ('tasks/__pycache__/__init__.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/__init__.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/advanced_image_processing.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/advanced_image_processing.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/beauty_processing.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/beauty_processing.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/ffmpeg_video_processor.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/ffmpeg_video_processor.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/image_filters.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/image_filters.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/optimized_video_processor.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/optimized_video_processor.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/parallel_video_processor.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/parallel_video_processor.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/pure_opencv_ffmpeg_processor.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/pure_opencv_ffmpeg_processor.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/truly_optimized_video_processor.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/truly_optimized_video_processor.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/video_processor.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/video_processor.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/video_tasks.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/video_tasks.cpython-39.pyc',
   'DATA'),
  ('tasks/beauty_processing.py',
   '/home/<USER>/projects/homework/backend/tasks/beauty_processing.py',
   'DATA'),
  ('tasks/image_edge_detection.py',
   '/home/<USER>/projects/homework/backend/tasks/image_edge_detection.py',
   'DATA'),
  ('tasks/image_fusion.py',
   '/home/<USER>/projects/homework/backend/tasks/image_fusion.py',
   'DATA'),
  ('tasks/image_gamma_correction.py',
   '/home/<USER>/projects/homework/backend/tasks/image_gamma_correction.py',
   'DATA'),
  ('tasks/image_grayscale.py',
   '/home/<USER>/projects/homework/backend/tasks/image_grayscale.py',
   'DATA'),
  ('tasks/image_sharpen.py',
   '/home/<USER>/projects/homework/backend/tasks/image_sharpen.py',
   'DATA'),
  ('tasks/image_stitching.py',
   '/home/<USER>/projects/homework/backend/tasks/image_stitching.py',
   'DATA'),
  ('tasks/opencv_ffmpeg_processor.py',
   '/home/<USER>/projects/homework/backend/tasks/opencv_ffmpeg_processor.py',
   'DATA'),
  ('tasks/texture_transfer.py',
   '/home/<USER>/projects/homework/backend/tasks/texture_transfer.py',
   'DATA'),
  ('tasks/video_binary.py',
   '/home/<USER>/projects/homework/backend/tasks/video_binary.py',
   'DATA'),
  ('tasks/video_blur.py',
   '/home/<USER>/projects/homework/backend/tasks/video_blur.py',
   'DATA'),
  ('tasks/video_edge_detection.py',
   '/home/<USER>/projects/homework/backend/tasks/video_edge_detection.py',
   'DATA'),
  ('tasks/video_extract_frame.py',
   '/home/<USER>/projects/homework/backend/tasks/video_extract_frame.py',
   'DATA'),
  ('tasks/video_grayscale.py',
   '/home/<USER>/projects/homework/backend/tasks/video_grayscale.py',
   'DATA'),
  ('tasks/video_processing/__init__.py',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/__init__.py',
   'DATA'),
  ('tasks/video_processing/__pycache__/__init__.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/__pycache__/__init__.cpython-312.pyc',
   'DATA'),
  ('tasks/video_processing/__pycache__/ffmpeg_processor.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/__pycache__/ffmpeg_processor.cpython-312.pyc',
   'DATA'),
  ('tasks/video_processing/__pycache__/opencv_processors.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/__pycache__/opencv_processors.cpython-312.pyc',
   'DATA'),
  ('tasks/video_processing/__pycache__/performance_config.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/__pycache__/performance_config.cpython-312.pyc',
   'DATA'),
  ('tasks/video_processing/ffmpeg_processor.py',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/ffmpeg_processor.py',
   'DATA'),
  ('tasks/video_processing/opencv_processors.py',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/opencv_processors.py',
   'DATA'),
  ('tasks/video_processing/performance_config.py',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/performance_config.py',
   'DATA'),
  ('tasks/video_resize.py',
   '/home/<USER>/projects/homework/backend/tasks/video_resize.py',
   'DATA'),
  ('tasks/video_tasks.py',
   '/home/<USER>/projects/homework/backend/tasks/video_tasks.py',
   'DATA'),
  ('tasks/video_thumbnail.py',
   '/home/<USER>/projects/homework/backend/tasks/video_thumbnail.py',
   'DATA'),
  ('tasks/video_transform.py',
   '/home/<USER>/projects/homework/backend/tasks/video_transform.py',
   'DATA'),
  ('utils/__init__.py',
   '/home/<USER>/projects/homework/backend/utils/__init__.py',
   'DATA'),
  ('utils/__pycache__/__init__.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/utils/__pycache__/__init__.cpython-312.pyc',
   'DATA'),
  ('utils/__pycache__/cache_manager.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/utils/__pycache__/cache_manager.cpython-312.pyc',
   'DATA'),
  ('utils/__pycache__/validation.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/utils/__pycache__/validation.cpython-312.pyc',
   'DATA'),
  ('utils/cache_manager.py',
   '/home/<USER>/projects/homework/backend/utils/cache_manager.py',
   'DATA'),
  ('utils/validation.py',
   '/home/<USER>/projects/homework/backend/utils/validation.py',
   'DATA')],
 '3.12.3 (main, Jun 18 2025, 17:59:45) [GCC 13.3.0]',
 [('pyi_rth_inspect',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PyInstaller/hooks/rthooks/pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('desktop_app_sync',
   '/home/<USER>/projects/homework/backend/desktop_app_sync.py',
   'PYSOURCE')],
 [('_distutils_hack',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/_distutils_hack/__init__.py',
   'PYMODULE'),
  ('importlib.util', '/usr/lib/python3.12/importlib/util.py', 'PYMODULE'),
  ('importlib._bootstrap_external',
   '/usr/lib/python3.12/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/usr/lib/python3.12/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('typing', '/usr/lib/python3.12/typing.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   '/usr/lib/python3.12/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/usr/lib/python3.12/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/usr/lib/python3.12/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/usr/lib/python3.12/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/usr/lib/python3.12/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/usr/lib/python3.12/importlib/metadata/_text.py',
   'PYMODULE'),
  ('email.message', '/usr/lib/python3.12/email/message.py', 'PYMODULE'),
  ('email.policy', '/usr/lib/python3.12/email/policy.py', 'PYMODULE'),
  ('email.contentmanager',
   '/usr/lib/python3.12/email/contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime', '/usr/lib/python3.12/email/quoprimime.py', 'PYMODULE'),
  ('string', '/usr/lib/python3.12/string.py', 'PYMODULE'),
  ('email.headerregistry',
   '/usr/lib/python3.12/email/headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/usr/lib/python3.12/email/_header_value_parser.py',
   'PYMODULE'),
  ('urllib', '/usr/lib/python3.12/urllib/__init__.py', 'PYMODULE'),
  ('email.iterators', '/usr/lib/python3.12/email/iterators.py', 'PYMODULE'),
  ('email.generator', '/usr/lib/python3.12/email/generator.py', 'PYMODULE'),
  ('copy', '/usr/lib/python3.12/copy.py', 'PYMODULE'),
  ('random', '/usr/lib/python3.12/random.py', 'PYMODULE'),
  ('statistics', '/usr/lib/python3.12/statistics.py', 'PYMODULE'),
  ('decimal', '/usr/lib/python3.12/decimal.py', 'PYMODULE'),
  ('_pydecimal', '/usr/lib/python3.12/_pydecimal.py', 'PYMODULE'),
  ('contextvars', '/usr/lib/python3.12/contextvars.py', 'PYMODULE'),
  ('fractions', '/usr/lib/python3.12/fractions.py', 'PYMODULE'),
  ('numbers', '/usr/lib/python3.12/numbers.py', 'PYMODULE'),
  ('hashlib', '/usr/lib/python3.12/hashlib.py', 'PYMODULE'),
  ('bisect', '/usr/lib/python3.12/bisect.py', 'PYMODULE'),
  ('email._encoded_words',
   '/usr/lib/python3.12/email/_encoded_words.py',
   'PYMODULE'),
  ('base64', '/usr/lib/python3.12/base64.py', 'PYMODULE'),
  ('getopt', '/usr/lib/python3.12/getopt.py', 'PYMODULE'),
  ('gettext', '/usr/lib/python3.12/gettext.py', 'PYMODULE'),
  ('struct', '/usr/lib/python3.12/struct.py', 'PYMODULE'),
  ('email.charset', '/usr/lib/python3.12/email/charset.py', 'PYMODULE'),
  ('email.encoders', '/usr/lib/python3.12/email/encoders.py', 'PYMODULE'),
  ('email.base64mime', '/usr/lib/python3.12/email/base64mime.py', 'PYMODULE'),
  ('email._policybase', '/usr/lib/python3.12/email/_policybase.py', 'PYMODULE'),
  ('email.header', '/usr/lib/python3.12/email/header.py', 'PYMODULE'),
  ('email.errors', '/usr/lib/python3.12/email/errors.py', 'PYMODULE'),
  ('email.utils', '/usr/lib/python3.12/email/utils.py', 'PYMODULE'),
  ('email._parseaddr', '/usr/lib/python3.12/email/_parseaddr.py', 'PYMODULE'),
  ('calendar', '/usr/lib/python3.12/calendar.py', 'PYMODULE'),
  ('argparse', '/usr/lib/python3.12/argparse.py', 'PYMODULE'),
  ('shutil', '/usr/lib/python3.12/shutil.py', 'PYMODULE'),
  ('tarfile', '/usr/lib/python3.12/tarfile.py', 'PYMODULE'),
  ('gzip', '/usr/lib/python3.12/gzip.py', 'PYMODULE'),
  ('_compression', '/usr/lib/python3.12/_compression.py', 'PYMODULE'),
  ('lzma', '/usr/lib/python3.12/lzma.py', 'PYMODULE'),
  ('bz2', '/usr/lib/python3.12/bz2.py', 'PYMODULE'),
  ('fnmatch', '/usr/lib/python3.12/fnmatch.py', 'PYMODULE'),
  ('urllib.parse', '/usr/lib/python3.12/urllib/parse.py', 'PYMODULE'),
  ('ipaddress', '/usr/lib/python3.12/ipaddress.py', 'PYMODULE'),
  ('datetime', '/usr/lib/python3.12/datetime.py', 'PYMODULE'),
  ('_pydatetime', '/usr/lib/python3.12/_pydatetime.py', 'PYMODULE'),
  ('_strptime', '/usr/lib/python3.12/_strptime.py', 'PYMODULE'),
  ('quopri', '/usr/lib/python3.12/quopri.py', 'PYMODULE'),
  ('inspect', '/usr/lib/python3.12/inspect.py', 'PYMODULE'),
  ('token', '/usr/lib/python3.12/token.py', 'PYMODULE'),
  ('importlib.machinery',
   '/usr/lib/python3.12/importlib/machinery.py',
   'PYMODULE'),
  ('dis', '/usr/lib/python3.12/dis.py', 'PYMODULE'),
  ('opcode', '/usr/lib/python3.12/opcode.py', 'PYMODULE'),
  ('ast', '/usr/lib/python3.12/ast.py', 'PYMODULE'),
  ('contextlib', '/usr/lib/python3.12/contextlib.py', 'PYMODULE'),
  ('textwrap', '/usr/lib/python3.12/textwrap.py', 'PYMODULE'),
  ('zipfile', '/usr/lib/python3.12/zipfile/__init__.py', 'PYMODULE'),
  ('zipfile._path',
   '/usr/lib/python3.12/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/usr/lib/python3.12/zipfile/_path/glob.py',
   'PYMODULE'),
  ('py_compile', '/usr/lib/python3.12/py_compile.py', 'PYMODULE'),
  ('email', '/usr/lib/python3.12/email/__init__.py', 'PYMODULE'),
  ('email.parser', '/usr/lib/python3.12/email/parser.py', 'PYMODULE'),
  ('email.feedparser', '/usr/lib/python3.12/email/feedparser.py', 'PYMODULE'),
  ('csv', '/usr/lib/python3.12/csv.py', 'PYMODULE'),
  ('importlib.readers', '/usr/lib/python3.12/importlib/readers.py', 'PYMODULE'),
  ('importlib.resources.readers',
   '/usr/lib/python3.12/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/usr/lib/python3.12/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/usr/lib/python3.12/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources',
   '/usr/lib/python3.12/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   '/usr/lib/python3.12/importlib/resources/_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/usr/lib/python3.12/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/usr/lib/python3.12/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('tempfile', '/usr/lib/python3.12/tempfile.py', 'PYMODULE'),
  ('tokenize', '/usr/lib/python3.12/tokenize.py', 'PYMODULE'),
  ('importlib._bootstrap',
   '/usr/lib/python3.12/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc', '/usr/lib/python3.12/importlib/_abc.py', 'PYMODULE'),
  ('importlib.abc', '/usr/lib/python3.12/importlib/abc.py', 'PYMODULE'),
  ('importlib', '/usr/lib/python3.12/importlib/__init__.py', 'PYMODULE'),
  ('setuptools',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/__init__.py',
   'PYMODULE'),
  ('setuptools.msvc',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/msvc.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor', '-', 'PYMODULE'),
  ('setuptools._distutils.errors',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/errors.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/util.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/debug.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/_log.py',
   'PYMODULE'),
  ('subprocess', '/usr/lib/python3.12/subprocess.py', 'PYMODULE'),
  ('selectors', '/usr/lib/python3.12/selectors.py', 'PYMODULE'),
  ('signal', '/usr/lib/python3.12/signal.py', 'PYMODULE'),
  ('setuptools._distutils.compat.py39',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/compat/py39.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/compilers/C/base.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE'),
  ('setuptools._distutils.compilers.C.msvc',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/compilers/C/msvc.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE'),
  ('setuptools._distutils.compat.numpy',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/compat/numpy.py',
   'PYMODULE'),
  ('jaraco', '-', 'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/functools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE'),
  ('sysconfig', '/usr/lib/python3.12/sysconfig.py', 'PYMODULE'),
  ('_sysconfigdata__x86_64-linux-gnu',
   '/usr/lib/python3.12/_sysconfigdata__x86_64-linux-gnu.py',
   'PYMODULE'),
  ('_aix_support', '/usr/lib/python3.12/_aix_support.py', 'PYMODULE'),
  ('pprint', '/usr/lib/python3.12/pprint.py', 'PYMODULE'),
  ('dataclasses', '/usr/lib/python3.12/dataclasses.py', 'PYMODULE'),
  ('setuptools._distutils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/version.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.compilers.C.errors',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/compilers/C/errors.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/more_itertools/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/more_itertools/recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/more_itertools/more.py',
   'PYMODULE'),
  ('queue', '/usr/lib/python3.12/queue.py', 'PYMODULE'),
  ('platform', '/usr/lib/python3.12/platform.py', 'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/command/build_ext.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/_msvccompiler.py',
   'PYMODULE'),
  ('concurrent.futures',
   '/usr/lib/python3.12/concurrent/futures/__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   '/usr/lib/python3.12/concurrent/futures/thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   '/usr/lib/python3.12/concurrent/futures/process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   '/usr/lib/python3.12/multiprocessing/synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   '/usr/lib/python3.12/multiprocessing/heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   '/usr/lib/python3.12/multiprocessing/resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   '/usr/lib/python3.12/multiprocessing/spawn.py',
   'PYMODULE'),
  ('runpy', '/usr/lib/python3.12/runpy.py', 'PYMODULE'),
  ('pkgutil', '/usr/lib/python3.12/pkgutil.py', 'PYMODULE'),
  ('zipimport', '/usr/lib/python3.12/zipimport.py', 'PYMODULE'),
  ('multiprocessing.util',
   '/usr/lib/python3.12/multiprocessing/util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   '/usr/lib/python3.12/multiprocessing/forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   '/usr/lib/python3.12/multiprocessing/process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   '/usr/lib/python3.12/multiprocessing/context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   '/usr/lib/python3.12/multiprocessing/popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   '/usr/lib/python3.12/multiprocessing/popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   '/usr/lib/python3.12/multiprocessing/popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   '/usr/lib/python3.12/multiprocessing/popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   '/usr/lib/python3.12/multiprocessing/sharedctypes.py',
   'PYMODULE'),
  ('ctypes', '/usr/lib/python3.12/ctypes/__init__.py', 'PYMODULE'),
  ('ctypes._endian', '/usr/lib/python3.12/ctypes/_endian.py', 'PYMODULE'),
  ('multiprocessing.pool',
   '/usr/lib/python3.12/multiprocessing/pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   '/usr/lib/python3.12/multiprocessing/dummy/__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   '/usr/lib/python3.12/multiprocessing/dummy/connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   '/usr/lib/python3.12/multiprocessing/managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   '/usr/lib/python3.12/multiprocessing/shared_memory.py',
   'PYMODULE'),
  ('secrets', '/usr/lib/python3.12/secrets.py', 'PYMODULE'),
  ('hmac', '/usr/lib/python3.12/hmac.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   '/usr/lib/python3.12/multiprocessing/reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   '/usr/lib/python3.12/multiprocessing/resource_sharer.py',
   'PYMODULE'),
  ('pickle', '/usr/lib/python3.12/pickle.py', 'PYMODULE'),
  ('_compat_pickle', '/usr/lib/python3.12/_compat_pickle.py', 'PYMODULE'),
  ('multiprocessing.queues',
   '/usr/lib/python3.12/multiprocessing/queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   '/usr/lib/python3.12/multiprocessing/connection.py',
   'PYMODULE'),
  ('xmlrpc.client', '/usr/lib/python3.12/xmlrpc/client.py', 'PYMODULE'),
  ('xmlrpc', '/usr/lib/python3.12/xmlrpc/__init__.py', 'PYMODULE'),
  ('xml.parsers.expat', '/usr/lib/python3.12/xml/parsers/expat.py', 'PYMODULE'),
  ('xml.parsers', '/usr/lib/python3.12/xml/parsers/__init__.py', 'PYMODULE'),
  ('xml', '/usr/lib/python3.12/xml/__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   '/usr/lib/python3.12/xml/sax/expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils', '/usr/lib/python3.12/xml/sax/saxutils.py', 'PYMODULE'),
  ('urllib.request', '/usr/lib/python3.12/urllib/request.py', 'PYMODULE'),
  ('getpass', '/usr/lib/python3.12/getpass.py', 'PYMODULE'),
  ('nturl2path', '/usr/lib/python3.12/nturl2path.py', 'PYMODULE'),
  ('ftplib', '/usr/lib/python3.12/ftplib.py', 'PYMODULE'),
  ('netrc', '/usr/lib/python3.12/netrc.py', 'PYMODULE'),
  ('mimetypes', '/usr/lib/python3.12/mimetypes.py', 'PYMODULE'),
  ('http.cookiejar', '/usr/lib/python3.12/http/cookiejar.py', 'PYMODULE'),
  ('http', '/usr/lib/python3.12/http/__init__.py', 'PYMODULE'),
  ('ssl', '/usr/lib/python3.12/ssl.py', 'PYMODULE'),
  ('urllib.response', '/usr/lib/python3.12/urllib/response.py', 'PYMODULE'),
  ('urllib.error', '/usr/lib/python3.12/urllib/error.py', 'PYMODULE'),
  ('xml.sax', '/usr/lib/python3.12/xml/sax/__init__.py', 'PYMODULE'),
  ('xml.sax.handler', '/usr/lib/python3.12/xml/sax/handler.py', 'PYMODULE'),
  ('xml.sax._exceptions',
   '/usr/lib/python3.12/xml/sax/_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader', '/usr/lib/python3.12/xml/sax/xmlreader.py', 'PYMODULE'),
  ('http.client', '/usr/lib/python3.12/http/client.py', 'PYMODULE'),
  ('concurrent.futures._base',
   '/usr/lib/python3.12/concurrent/futures/_base.py',
   'PYMODULE'),
  ('concurrent', '/usr/lib/python3.12/concurrent/__init__.py', 'PYMODULE'),
  ('setuptools._distutils.extension',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/extension.py',
   'PYMODULE'),
  ('site', '/usr/lib/python3.12/site.py', 'PYMODULE'),
  ('sitecustomize', '/usr/lib/python3.12/sitecustomize.py', 'PYMODULE'),
  ('rlcompleter', '/usr/lib/python3.12/rlcompleter.py', 'PYMODULE'),
  ('_sitebuiltins', '/usr/lib/python3.12/_sitebuiltins.py', 'PYMODULE'),
  ('pydoc', '/usr/lib/python3.12/pydoc.py', 'PYMODULE'),
  ('webbrowser', '/usr/lib/python3.12/webbrowser.py', 'PYMODULE'),
  ('shlex', '/usr/lib/python3.12/shlex.py', 'PYMODULE'),
  ('http.server', '/usr/lib/python3.12/http/server.py', 'PYMODULE'),
  ('socketserver', '/usr/lib/python3.12/socketserver.py', 'PYMODULE'),
  ('html', '/usr/lib/python3.12/html/__init__.py', 'PYMODULE'),
  ('html.entities', '/usr/lib/python3.12/html/entities.py', 'PYMODULE'),
  ('pydoc_data.topics', '/usr/lib/python3.12/pydoc_data/topics.py', 'PYMODULE'),
  ('pydoc_data', '/usr/lib/python3.12/pydoc_data/__init__.py', 'PYMODULE'),
  ('tty', '/usr/lib/python3.12/tty.py', 'PYMODULE'),
  ('setuptools._distutils.core',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/core.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/dist.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/versionpredicate.py',
   'PYMODULE'),
  ('configparser', '/usr/lib/python3.12/configparser.py', 'PYMODULE'),
  ('packaging.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/packaging/utils.py',
   'PYMODULE'),
  ('packaging',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/packaging/__init__.py',
   'PYMODULE'),
  ('packaging._musllinux',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/packaging/_musllinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/packaging/_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/packaging/_manylinux.py',
   'PYMODULE'),
  ('packaging.version',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/packaging/version.py',
   'PYMODULE'),
  ('packaging._structures',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/packaging/_structures.py',
   'PYMODULE'),
  ('packaging.tags',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/cmd.py',
   'PYMODULE'),
  ('setuptools.warnings',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/warnings.py',
   'PYMODULE'),
  ('setuptools.version',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/version.py',
   'PYMODULE'),
  ('setuptools._importlib',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_importlib.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/compat/py311.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/compat/py39.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata.compat',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/compat/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata/_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/zipp/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.glob',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/zipp/glob.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat.py310',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/zipp/compat/py310.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp.compat',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/zipp/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.extension',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/extension.py',
   'PYMODULE'),
  ('setuptools._path',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_path.py',
   'PYMODULE'),
  ('setuptools.dist',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/dist.py',
   'PYMODULE'),
  ('setuptools.command.bdist_wheel',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/command/bdist_wheel.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.macosx_libfile',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/macosx_libfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/__init__.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/command/egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/filelist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/command/_requirestxt.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/context.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/compat/py38.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile.compat',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/backports/tarfile/compat/__init__.py',
   'PYMODULE'),
  ('backports',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/backports/__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/glob.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/command/setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/command/sdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/command/sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/command/build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/command/build.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/command/bdist_egg.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/unicode_utils.py',
   'PYMODULE'),
  ('setuptools.compat.py39',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/compat/py39.py',
   'PYMODULE'),
  ('setuptools.compat',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/compat/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py311',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/compat/py311.py',
   'PYMODULE'),
  ('packaging.requirements',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/packaging/requirements.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/packaging/_tokenizer.py',
   'PYMODULE'),
  ('packaging._parser',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.wheelfile',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/wheelfile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.util',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/util.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.tags',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.convert',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/convert.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.metadata',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/vendored/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.pack',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/pack.py',
   'PYMODULE'),
  ('setuptools._vendor.wheel.cli.unpack',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/wheel/cli/unpack.py',
   'PYMODULE'),
  ('setuptools.installer',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/installer.py',
   'PYMODULE'),
  ('setuptools.wheel',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/wheel.py',
   'PYMODULE'),
  ('setuptools._discovery',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_discovery.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/log.py',
   'PYMODULE'),
  ('setuptools.errors',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/config/setupcfg.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/config/expand.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/config/pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/error_reporting.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/config/_validate_pyproject/formats.py',
   'PYMODULE'),
  ('packaging.licenses',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/packaging/licenses/__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/packaging/licenses/_spdx.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/packaging/__init__.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/compat/py310.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/tomli/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/tomli/_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/tomli/_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/tomli/_re.py',
   'PYMODULE'),
  ('tomllib', '/usr/lib/python3.12/tomllib/__init__.py', 'PYMODULE'),
  ('tomllib._parser', '/usr/lib/python3.12/tomllib/_parser.py', 'PYMODULE'),
  ('tomllib._types', '/usr/lib/python3.12/tomllib/_types.py', 'PYMODULE'),
  ('tomllib._re', '/usr/lib/python3.12/tomllib/_re.py', 'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/config/_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/config/__init__.py',
   'PYMODULE'),
  ('setuptools._static',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_static.py',
   'PYMODULE'),
  ('packaging.specifiers',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/packaging/specifiers.py',
   'PYMODULE'),
  ('packaging.markers',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/packaging/markers.py',
   'PYMODULE'),
  ('glob', '/usr/lib/python3.12/glob.py', 'PYMODULE'),
  ('setuptools._shutil',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_shutil.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/windows_support.py',
   'PYMODULE'),
  ('ctypes.wintypes', '/usr/lib/python3.12/ctypes/wintypes.py', 'PYMODULE'),
  ('setuptools.command',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/command/__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_distutils/command/bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_imp.py',
   'PYMODULE'),
  ('setuptools.logging',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/monkey.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_normalization.py',
   'PYMODULE'),
  ('_distutils_hack.override',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/_distutils_hack/override.py',
   'PYMODULE'),
  ('__future__', '/usr/lib/python3.12/__future__.py', 'PYMODULE'),
  ('pkg_resources',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pkg_resources/__init__.py',
   'PYMODULE'),
  ('packaging.metadata',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/packaging/metadata.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.android',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/android.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.unix',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/unix.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.macos',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/macos.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.windows',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/windows.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.version',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/version.py',
   'PYMODULE'),
  ('setuptools._vendor.platformdirs.api',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/platformdirs/api.py',
   'PYMODULE'),
  ('plistlib', '/usr/lib/python3.12/plistlib.py', 'PYMODULE'),
  ('tasks.video_thumbnail',
   '/home/<USER>/projects/homework/backend/tasks/video_thumbnail.py',
   'PYMODULE'),
  ('tasks.video_extract_frame',
   '/home/<USER>/projects/homework/backend/tasks/video_extract_frame.py',
   'PYMODULE'),
  ('tasks.video_transform',
   '/home/<USER>/projects/homework/backend/tasks/video_transform.py',
   'PYMODULE'),
  ('tasks.opencv_ffmpeg_processor',
   '/home/<USER>/projects/homework/backend/tasks/opencv_ffmpeg_processor.py',
   'PYMODULE'),
  ('tasks.video_processing',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/__init__.py',
   'PYMODULE'),
  ('tasks.video_processing.ffmpeg_processor',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/ffmpeg_processor.py',
   'PYMODULE'),
  ('tasks.video_processing.opencv_processors',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/opencv_processors.py',
   'PYMODULE'),
  ('tasks.video_processing.performance_config',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/performance_config.py',
   'PYMODULE'),
  ('tasks.video_edge_detection',
   '/home/<USER>/projects/homework/backend/tasks/video_edge_detection.py',
   'PYMODULE'),
  ('tasks.video_blur',
   '/home/<USER>/projects/homework/backend/tasks/video_blur.py',
   'PYMODULE'),
  ('tasks.video_binary',
   '/home/<USER>/projects/homework/backend/tasks/video_binary.py',
   'PYMODULE'),
  ('tasks.video_resize',
   '/home/<USER>/projects/homework/backend/tasks/video_resize.py',
   'PYMODULE'),
  ('tasks.video_grayscale',
   '/home/<USER>/projects/homework/backend/tasks/video_grayscale.py',
   'PYMODULE'),
  ('tasks.video_tasks',
   '/home/<USER>/projects/homework/backend/tasks/video_tasks.py',
   'PYMODULE'),
  ('tasks.beauty_processing',
   '/home/<USER>/projects/homework/backend/tasks/beauty_processing.py',
   'PYMODULE'),
  ('tasks.texture_transfer',
   '/home/<USER>/projects/homework/backend/tasks/texture_transfer.py',
   'PYMODULE'),
  ('tasks.image_stitching',
   '/home/<USER>/projects/homework/backend/tasks/image_stitching.py',
   'PYMODULE'),
  ('tasks.image_fusion',
   '/home/<USER>/projects/homework/backend/tasks/image_fusion.py',
   'PYMODULE'),
  ('tasks.image_edge_detection',
   '/home/<USER>/projects/homework/backend/tasks/image_edge_detection.py',
   'PYMODULE'),
  ('tasks.image_grayscale',
   '/home/<USER>/projects/homework/backend/tasks/image_grayscale.py',
   'PYMODULE'),
  ('tasks.image_gamma_correction',
   '/home/<USER>/projects/homework/backend/tasks/image_gamma_correction.py',
   'PYMODULE'),
  ('tasks.image_sharpen',
   '/home/<USER>/projects/homework/backend/tasks/image_sharpen.py',
   'PYMODULE'),
  ('utils.cache_manager',
   '/home/<USER>/projects/homework/backend/utils/cache_manager.py',
   'PYMODULE'),
  ('utils',
   '/home/<USER>/projects/homework/backend/utils/__init__.py',
   'PYMODULE'),
  ('tasks',
   '/home/<USER>/projects/homework/backend/tasks/__init__.py',
   'PYMODULE'),
  ('routes.process',
   '/home/<USER>/projects/homework/backend/routes/process.py',
   'PYMODULE'),
  ('routes',
   '/home/<USER>/projects/homework/backend/routes/__init__.py',
   'PYMODULE'),
  ('app', '/home/<USER>/projects/homework/backend/app.py', 'PYMODULE'),
  ('json', '/usr/lib/python3.12/json/__init__.py', 'PYMODULE'),
  ('json.encoder', '/usr/lib/python3.12/json/encoder.py', 'PYMODULE'),
  ('json.decoder', '/usr/lib/python3.12/json/decoder.py', 'PYMODULE'),
  ('json.scanner', '/usr/lib/python3.12/json/scanner.py', 'PYMODULE'),
  ('threading', '/usr/lib/python3.12/threading.py', 'PYMODULE'),
  ('_threading_local', '/usr/lib/python3.12/_threading_local.py', 'PYMODULE'),
  ('multiprocessing',
   '/usr/lib/python3.12/multiprocessing/__init__.py',
   'PYMODULE'),
  ('pathlib', '/usr/lib/python3.12/pathlib.py', 'PYMODULE'),
  ('psutil',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/psutil/__init__.py',
   'PYMODULE'),
  ('psutil._pslinux',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/psutil/_pslinux.py',
   'PYMODULE'),
  ('psutil._psposix',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/psutil/_psposix.py',
   'PYMODULE'),
  ('psutil._common',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/psutil/_common.py',
   'PYMODULE'),
  ('curses', '/usr/lib/python3.12/curses/__init__.py', 'PYMODULE'),
  ('curses.has_key', '/usr/lib/python3.12/curses/has_key.py', 'PYMODULE'),
  ('moviepy.audio.fx',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/audio/fx/__init__.py',
   'PYMODULE'),
  ('moviepy.audio',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/audio/__init__.py',
   'PYMODULE'),
  ('moviepy.video.fx',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/__init__.py',
   'PYMODULE'),
  ('moviepy.video',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/__init__.py',
   'PYMODULE'),
  ('moviepy.video.io.VideoFileClip',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/io/VideoFileClip.py',
   'PYMODULE'),
  ('moviepy.video.io',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/io/__init__.py',
   'PYMODULE'),
  ('moviepy.video.VideoClip',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/VideoClip.py',
   'PYMODULE'),
  ('moviepy.video.compositing.CompositeVideoClip',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/compositing/CompositeVideoClip.py',
   'PYMODULE'),
  ('moviepy.video.compositing',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/compositing/__init__.py',
   'PYMODULE'),
  ('moviepy.audio.AudioClip',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/audio/AudioClip.py',
   'PYMODULE'),
  ('moviepy.audio.io.ffmpeg_audiowriter',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/audio/io/ffmpeg_audiowriter.py',
   'PYMODULE'),
  ('moviepy.audio.io',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/audio/io/__init__.py',
   'PYMODULE'),
  ('tqdm',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/tqdm/__init__.py',
   'PYMODULE'),
  ('tqdm.notebook',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/tqdm/notebook.py',
   'PYMODULE'),
  ('tqdm.version',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/tqdm/version.py',
   'PYMODULE'),
  ('tqdm._dist_ver',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/tqdm/_dist_ver.py',
   'PYMODULE'),
  ('tqdm.std',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/tqdm/std.py',
   'PYMODULE'),
  ('tqdm.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/tqdm/utils.py',
   'PYMODULE'),
  ('tqdm.gui',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/tqdm/gui.py',
   'PYMODULE'),
  ('tqdm.cli',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/tqdm/cli.py',
   'PYMODULE'),
  ('tqdm._tqdm_pandas',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/tqdm/_tqdm_pandas.py',
   'PYMODULE'),
  ('tqdm._monitor',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/tqdm/_monitor.py',
   'PYMODULE'),
  ('moviepy.video.compositing.concatenate',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/compositing/concatenate.py',
   'PYMODULE'),
  ('moviepy.video.compositing.on_color',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/compositing/on_color.py',
   'PYMODULE'),
  ('moviepy.video.tools.drawing',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/tools/drawing.py',
   'PYMODULE'),
  ('moviepy.video.tools',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/tools/__init__.py',
   'PYMODULE'),
  ('moviepy.video.io.gif_writers',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/io/gif_writers.py',
   'PYMODULE'),
  ('moviepy.video.io.ffmpeg_writer',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/io/ffmpeg_writer.py',
   'PYMODULE'),
  ('proglog.proglog',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/proglog/proglog.py',
   'PYMODULE'),
  ('moviepy.tools',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/tools.py',
   'PYMODULE'),
  ('moviepy.decorators',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/decorators.py',
   'PYMODULE'),
  ('decorator',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/decorator.py',
   'PYMODULE'),
  ('moviepy.config',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/config.py',
   'PYMODULE'),
  ('imageio.plugins.ffmpeg',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/ffmpeg.py',
   'PYMODULE'),
  ('imageio.plugins',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/__init__.py',
   'PYMODULE'),
  ('imageio.core',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/core/__init__.py',
   'PYMODULE'),
  ('imageio.core.format',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/core/format.py',
   'PYMODULE'),
  ('imageio.core.imopen',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/core/imopen.py',
   'PYMODULE'),
  ('imageio.config.extensions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/config/extensions.py',
   'PYMODULE'),
  ('imageio.config.plugins',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/config/plugins.py',
   'PYMODULE'),
  ('imageio.core.legacy_plugin_wrapper',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/core/legacy_plugin_wrapper.py',
   'PYMODULE'),
  ('imageio.core.v3_plugin_api',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/core/v3_plugin_api.py',
   'PYMODULE'),
  ('imageio.typing',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/typing.py',
   'PYMODULE'),
  ('numpy.typing',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/typing/__init__.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/testing/__init__.py',
   'PYMODULE'),
  ('numpy.testing.overrides',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/testing/overrides.py',
   'PYMODULE'),
  ('numpy.lib.recfunctions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/recfunctions.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/_iotools.py',
   'PYMODULE'),
  ('numpy.compat',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/compat/__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/compat/py3k.py',
   'PYMODULE'),
  ('numpy._utils._inspect',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/_utils/_inspect.py',
   'PYMODULE'),
  ('numpy._utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/_utils/__init__.py',
   'PYMODULE'),
  ('numpy._utils._convertions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/_utils/_convertions.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/arrayprint.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/stride_tricks.py',
   'PYMODULE'),
  ('numpy._globals',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/_globals.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/_exceptions.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/_ufunc_config.py',
   'PYMODULE'),
  ('numpy.exceptions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/exceptions.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/_string_helpers.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/shape_base.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/multiarray.py',
   'PYMODULE'),
  ('numpy.core',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/__init__.py',
   'PYMODULE'),
  ('numpy.core._internal',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/_internal.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/einsumfunc.py',
   'PYMODULE'),
  ('numpy.core._machar',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/_machar.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/function_base.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/memmap.py',
   'PYMODULE'),
  ('numpy.core.records',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/records.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/defchararray.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/getlimits.py',
   'PYMODULE'),
  ('numpy.version',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/version.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/ma/mrecords.py',
   'PYMODULE'),
  ('numpy.ma',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/ma/__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/ma/extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/index_tricks.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/matrixlib/__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/matrixlib/defmatrix.py',
   'PYMODULE'),
  ('numpy.linalg',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/linalg/__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/linalg/linalg.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/histograms.py',
   'PYMODULE'),
  ('numpy.ma.core',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/ma/core.py',
   'PYMODULE'),
  ('numpy.lib',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/npyio.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/type_check.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/lib/mixins.py',
   'PYMODULE'),
  ('numpy.core.umath',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/overrides.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/testing/_private/extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/testing/_private/utils.py',
   'PYMODULE'),
  ('doctest', '/usr/lib/python3.12/doctest.py', 'PYMODULE'),
  ('pdb', '/usr/lib/python3.12/pdb.py', 'PYMODULE'),
  ('code', '/usr/lib/python3.12/code.py', 'PYMODULE'),
  ('codeop', '/usr/lib/python3.12/codeop.py', 'PYMODULE'),
  ('bdb', '/usr/lib/python3.12/bdb.py', 'PYMODULE'),
  ('cmd', '/usr/lib/python3.12/cmd.py', 'PYMODULE'),
  ('difflib', '/usr/lib/python3.12/difflib.py', 'PYMODULE'),
  ('numpy.testing._private',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/testing/_private/__init__.py',
   'PYMODULE'),
  ('numpy.array_api',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/array_api/__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/array_api/_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/array_api/_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/array_api/_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/array_api/_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/array_api/_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/array_api/_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/array_api/_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/array_api/_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/array_api/linalg.py',
   'PYMODULE'),
  ('numpy.array_api._indexing_functions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/array_api/_indexing_functions.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/array_api/_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/array_api/_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/array_api/_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/array_api/_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/array_api/_constants.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/_typing/_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/_typing/_array_like.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/_typing/_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/_typing/__init__.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/_typing/_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/_typing/_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/_typing/_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/_typing/_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/_typing/_nbit.py',
   'PYMODULE'),
  ('imageio.config',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/config/__init__.py',
   'PYMODULE'),
  ('imageio.core.request',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/core/request.py',
   'PYMODULE'),
  ('imageio.core.fetching',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/core/fetching.py',
   'PYMODULE'),
  ('imageio.core.findlib',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/core/findlib.py',
   'PYMODULE'),
  ('imageio.core.util',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/core/util.py',
   'PYMODULE'),
  ('imageio_ffmpeg',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio_ffmpeg/__init__.py',
   'PYMODULE'),
  ('imageio_ffmpeg.binaries',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio_ffmpeg/binaries/__init__.py',
   'PYMODULE'),
  ('imageio_ffmpeg._utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio_ffmpeg/_utils.py',
   'PYMODULE'),
  ('imageio_ffmpeg._io',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio_ffmpeg/_io.py',
   'PYMODULE'),
  ('imageio_ffmpeg._parsing',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio_ffmpeg/_parsing.py',
   'PYMODULE'),
  ('imageio_ffmpeg._definitions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio_ffmpeg/_definitions.py',
   'PYMODULE'),
  ('moviepy.config_defaults',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/config_defaults.py',
   'PYMODULE'),
  ('moviepy.compat',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/compat.py',
   'PYMODULE'),
  ('imageio',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/__init__.py',
   'PYMODULE'),
  ('imageio.plugins.tifffile_v3',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/tifffile_v3.py',
   'PYMODULE'),
  ('imageio.plugins.tifffile',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/tifffile.py',
   'PYMODULE'),
  ('imageio.plugins.swf',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/swf.py',
   'PYMODULE'),
  ('imageio.plugins.spe',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/spe.py',
   'PYMODULE'),
  ('imageio.plugins.simpleitk',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/simpleitk.py',
   'PYMODULE'),
  ('imageio.plugins.rawpy',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/rawpy.py',
   'PYMODULE'),
  ('imageio.plugins.pyav',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/pyav.py',
   'PYMODULE'),
  ('imageio.plugins.pillowmulti',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/pillowmulti.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/GifImagePlugin.py',
   'PYMODULE'),
  ('PIL._typing',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/_typing.py',
   'PYMODULE'),
  ('PIL._util',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/_util.py',
   'PYMODULE'),
  ('PIL._binary',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/_binary.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/PaletteFile.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImageColor.py',
   'PYMODULE'),
  ('colorsys', '/usr/lib/python3.12/colorsys.py', 'PYMODULE'),
  ('PIL.GimpPaletteFile',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImageOps.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ExifTags.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImageMath.py',
   'PYMODULE'),
  ('PIL._deprecate',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/_deprecate.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImageFile.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImageChops.py',
   'PYMODULE'),
  ('imageio.plugins.pillow_legacy',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/pillow_legacy.py',
   'PYMODULE'),
  ('imageio.plugins.pillow_info',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/pillow_info.py',
   'PYMODULE'),
  ('imageio.plugins.pillow',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/pillow.py',
   'PYMODULE'),
  ('imageio.plugins.opencv',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/opencv.py',
   'PYMODULE'),
  ('imageio.plugins.npz',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/npz.py',
   'PYMODULE'),
  ('imageio.plugins.lytro',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/lytro.py',
   'PYMODULE'),
  ('imageio.plugins.grab',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/grab.py',
   'PYMODULE'),
  ('PIL.ImageGrab',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImageGrab.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImageWin.py',
   'PYMODULE'),
  ('imageio.plugins.gdal',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/gdal.py',
   'PYMODULE'),
  ('imageio.plugins.freeimagemulti',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/freeimagemulti.py',
   'PYMODULE'),
  ('imageio.plugins.freeimage',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/freeimage.py',
   'PYMODULE'),
  ('imageio.plugins.fits',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/fits.py',
   'PYMODULE'),
  ('imageio.plugins.feisem',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/feisem.py',
   'PYMODULE'),
  ('imageio.plugins.example',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/example.py',
   'PYMODULE'),
  ('imageio.plugins.dicom',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/dicom.py',
   'PYMODULE'),
  ('imageio.plugins.bsdf',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/bsdf.py',
   'PYMODULE'),
  ('imageio.plugins._tifffile',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/_tifffile.py',
   'PYMODULE'),
  ('optparse', '/usr/lib/python3.12/optparse.py', 'PYMODULE'),
  ('xml.etree.cElementTree',
   '/usr/lib/python3.12/xml/etree/cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   '/usr/lib/python3.12/xml/etree/ElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   '/usr/lib/python3.12/xml/etree/ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   '/usr/lib/python3.12/xml/etree/ElementPath.py',
   'PYMODULE'),
  ('xml.etree', '/usr/lib/python3.12/xml/etree/__init__.py', 'PYMODULE'),
  ('imageio.plugins._swf',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/_swf.py',
   'PYMODULE'),
  ('imageio.plugins._freeimage',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/_freeimage.py',
   'PYMODULE'),
  ('imageio.plugins._dicom',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/_dicom.py',
   'PYMODULE'),
  ('imageio.plugins._bsdf',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/plugins/_bsdf.py',
   'PYMODULE'),
  ('imageio.v3',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/v3.py',
   'PYMODULE'),
  ('imageio.v2',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio/v2.py',
   'PYMODULE'),
  ('proglog',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/proglog/__init__.py',
   'PYMODULE'),
  ('proglog.version',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/proglog/version.py',
   'PYMODULE'),
  ('moviepy.video.io.ffmpeg_reader',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/io/ffmpeg_reader.py',
   'PYMODULE'),
  ('moviepy.Clip',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/Clip.py',
   'PYMODULE'),
  ('moviepy.audio.io.AudioFileClip',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/audio/io/AudioFileClip.py',
   'PYMODULE'),
  ('moviepy.audio.io.readers',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/audio/io/readers.py',
   'PYMODULE'),
  ('moviepy.editor',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/editor.py',
   'PYMODULE'),
  ('moviepy.audio.io.preview',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/audio/io/preview.py',
   'PYMODULE'),
  ('moviepy.video.io.preview',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/io/preview.py',
   'PYMODULE'),
  ('moviepy.video.io.sliders',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/io/sliders.py',
   'PYMODULE'),
  ('moviepy.video.io.html_tools',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/io/html_tools.py',
   'PYMODULE'),
  ('moviepy.video.io.ffmpeg_tools',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/io/ffmpeg_tools.py',
   'PYMODULE'),
  ('moviepy.video.compositing.transitions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/compositing/transitions.py',
   'PYMODULE'),
  ('moviepy.video.fx.fadeout',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/fadeout.py',
   'PYMODULE'),
  ('moviepy.video.fx.fadein',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/fadein.py',
   'PYMODULE'),
  ('moviepy.audio.fx.all',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/audio/fx/all/__init__.py',
   'PYMODULE'),
  ('moviepy.audio.fx.volumex',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/audio/fx/volumex.py',
   'PYMODULE'),
  ('moviepy.audio.fx.audio_normalize',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/audio/fx/audio_normalize.py',
   'PYMODULE'),
  ('moviepy.audio.fx.audio_loop',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/audio/fx/audio_loop.py',
   'PYMODULE'),
  ('moviepy.audio.fx.audio_left_right',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/audio/fx/audio_left_right.py',
   'PYMODULE'),
  ('moviepy.audio.fx.audio_fadeout',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/audio/fx/audio_fadeout.py',
   'PYMODULE'),
  ('moviepy.audio.fx.audio_fadein',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/audio/fx/audio_fadein.py',
   'PYMODULE'),
  ('moviepy.video.fx.all',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/all/__init__.py',
   'PYMODULE'),
  ('moviepy.video.fx.time_symmetrize',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/time_symmetrize.py',
   'PYMODULE'),
  ('moviepy.video.fx.time_mirror',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/time_mirror.py',
   'PYMODULE'),
  ('moviepy.video.fx.supersample',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/supersample.py',
   'PYMODULE'),
  ('moviepy.video.fx.speedx',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/speedx.py',
   'PYMODULE'),
  ('moviepy.video.fx.scroll',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/scroll.py',
   'PYMODULE'),
  ('moviepy.video.fx.rotate',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/rotate.py',
   'PYMODULE'),
  ('moviepy.video.fx.resize',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/resize.py',
   'PYMODULE'),
  ('moviepy.video.fx.painting',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/painting.py',
   'PYMODULE'),
  ('moviepy.video.fx.mirror_y',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/mirror_y.py',
   'PYMODULE'),
  ('moviepy.video.fx.mirror_x',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/mirror_x.py',
   'PYMODULE'),
  ('moviepy.video.fx.mask_or',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/mask_or.py',
   'PYMODULE'),
  ('moviepy.video.fx.mask_color',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/mask_color.py',
   'PYMODULE'),
  ('moviepy.video.fx.mask_and',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/mask_and.py',
   'PYMODULE'),
  ('moviepy.video.fx.margin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/margin.py',
   'PYMODULE'),
  ('moviepy.video.fx.make_loopable',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/make_loopable.py',
   'PYMODULE'),
  ('moviepy.video.fx.lum_contrast',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/lum_contrast.py',
   'PYMODULE'),
  ('moviepy.video.fx.loop',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/loop.py',
   'PYMODULE'),
  ('moviepy.video.fx.invert_colors',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/invert_colors.py',
   'PYMODULE'),
  ('moviepy.video.fx.headblur',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/headblur.py',
   'PYMODULE'),
  ('moviepy.video.fx.gamma_corr',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/gamma_corr.py',
   'PYMODULE'),
  ('moviepy.video.fx.freeze_region',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/freeze_region.py',
   'PYMODULE'),
  ('moviepy.video.fx.freeze',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/freeze.py',
   'PYMODULE'),
  ('moviepy.video.fx.even_size',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/even_size.py',
   'PYMODULE'),
  ('moviepy.video.fx.crop',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/crop.py',
   'PYMODULE'),
  ('moviepy.video.fx.colorx',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/colorx.py',
   'PYMODULE'),
  ('moviepy.video.fx.blink',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/blink.py',
   'PYMODULE'),
  ('moviepy.video.fx.blackwhite',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/blackwhite.py',
   'PYMODULE'),
  ('moviepy.video.fx.accel_decel',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/fx/accel_decel.py',
   'PYMODULE'),
  ('moviepy.video.io.downloader',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/io/downloader.py',
   'PYMODULE'),
  ('requests',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/requests/__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/requests/status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/requests/structures.py',
   'PYMODULE'),
  ('requests.compat',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/requests/compat.py',
   'PYMODULE'),
  ('http.cookies', '/usr/lib/python3.12/http/cookies.py', 'PYMODULE'),
  ('requests.models',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/requests/models.py',
   'PYMODULE'),
  ('idna',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/idna/__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/idna/package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/idna/intranges.py',
   'PYMODULE'),
  ('idna.core',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/idna/core.py',
   'PYMODULE'),
  ('idna.uts46data',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/idna/uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/idna/idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/requests/hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/requests/cookies.py',
   'PYMODULE'),
  ('requests.auth',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/requests/auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/requests/_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/util/__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/util/wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/util/url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/util/util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/util/timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/util/ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/util/ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/util/retry.py',
   'PYMODULE'),
  ('urllib3.response',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/response.py',
   'PYMODULE'),
  ('urllib3.connection',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/util/ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/http2/probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/http2/__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/http2/connection.py',
   'PYMODULE'),
  ('urllib3._collections',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/_collections.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/util/proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/util/response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/util/request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/util/connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/fields.py',
   'PYMODULE'),
  ('requests.api',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/requests/api.py',
   'PYMODULE'),
  ('requests.sessions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/requests/sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/requests/adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/contrib/socks.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/requests/__version__.py',
   'PYMODULE'),
  ('requests.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/requests/utils.py',
   'PYMODULE'),
  ('requests.certs',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/requests/certs.py',
   'PYMODULE'),
  ('certifi',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/certifi/__init__.py',
   'PYMODULE'),
  ('certifi.core',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/certifi/core.py',
   'PYMODULE'),
  ('requests.packages',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/requests/packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/exceptions.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/contrib/pyopenssl.py',
   'PYMODULE'),
  ('urllib3.contrib',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/contrib/__init__.py',
   'PYMODULE'),
  ('charset_normalizer',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/charset_normalizer/__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/charset_normalizer/version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/charset_normalizer/utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/charset_normalizer/constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/charset_normalizer/models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/charset_normalizer/cd.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/charset_normalizer/legacy.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/charset_normalizer/api.py',
   'PYMODULE'),
  ('requests.exceptions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/requests/exceptions.py',
   'PYMODULE'),
  ('urllib3',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/contrib/emscripten/__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/contrib/emscripten/connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/contrib/emscripten/response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/contrib/emscripten/request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/urllib3/contrib/emscripten/fetch.py',
   'PYMODULE'),
  ('moviepy.video.io.ImageSequenceClip',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/video/io/ImageSequenceClip.py',
   'PYMODULE'),
  ('moviepy',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/__init__.py',
   'PYMODULE'),
  ('moviepy.version',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/moviepy/version.py',
   'PYMODULE'),
  ('PIL.ImageEnhance',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImageEnhance.py',
   'PYMODULE'),
  ('PIL.ImageStat',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImageStat.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImageFilter.py',
   'PYMODULE'),
  ('PIL.Image',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/PdfParser.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.AvifImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/AvifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImageCms.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/JpegPresets.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/ImageMode.py',
   'PYMODULE'),
  ('PIL',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/__init__.py',
   'PYMODULE'),
  ('PIL._version',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/_version.py',
   'PYMODULE'),
  ('billiard.pool',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/pool.py',
   'PYMODULE'),
  ('billiard._win',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/_win.py',
   'PYMODULE'),
  ('billiard.exceptions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/exceptions.py',
   'PYMODULE'),
  ('billiard.dummy',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/dummy/__init__.py',
   'PYMODULE'),
  ('billiard.connection',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/connection.py',
   'PYMODULE'),
  ('billiard.resource_sharer',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/resource_sharer.py',
   'PYMODULE'),
  ('billiard.process',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/process.py',
   'PYMODULE'),
  ('billiard.context',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/context.py',
   'PYMODULE'),
  ('billiard.popen_spawn_win32',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/popen_spawn_win32.py',
   'PYMODULE'),
  ('billiard.popen_forkserver',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/popen_forkserver.py',
   'PYMODULE'),
  ('billiard.popen_spawn_posix',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/popen_spawn_posix.py',
   'PYMODULE'),
  ('billiard.semaphore_tracker',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/semaphore_tracker.py',
   'PYMODULE'),
  ('billiard.popen_fork',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/popen_fork.py',
   'PYMODULE'),
  ('billiard.forkserver',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/forkserver.py',
   'PYMODULE'),
  ('billiard.spawn',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/spawn.py',
   'PYMODULE'),
  ('billiard.sharedctypes',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/sharedctypes.py',
   'PYMODULE'),
  ('billiard.heap',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/heap.py',
   'PYMODULE'),
  ('billiard.queues',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/queues.py',
   'PYMODULE'),
  ('billiard.synchronize',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/synchronize.py',
   'PYMODULE'),
  ('billiard.managers',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/managers.py',
   'PYMODULE'),
  ('billiard._ext',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/_ext.py',
   'PYMODULE'),
  ('billiard.reduction',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/reduction.py',
   'PYMODULE'),
  ('billiard.einfo',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/einfo.py',
   'PYMODULE'),
  ('billiard.compat',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/compat.py',
   'PYMODULE'),
  ('billiard.common',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/common.py',
   'PYMODULE'),
  ('billiard.util',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/util.py',
   'PYMODULE'),
  ('billiard',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/billiard/__init__.py',
   'PYMODULE'),
  ('kombu.serialization',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/serialization.py',
   'PYMODULE'),
  ('kombu.utils.json',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/utils/json.py',
   'PYMODULE'),
  ('kombu.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/utils/__init__.py',
   'PYMODULE'),
  ('kombu.utils.uuid',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/utils/uuid.py',
   'PYMODULE'),
  ('kombu.utils.objects',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/utils/objects.py',
   'PYMODULE'),
  ('kombu.utils.imports',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/utils/imports.py',
   'PYMODULE'),
  ('kombu.utils.functional',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/utils/functional.py',
   'PYMODULE'),
  ('vine.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/vine/utils.py',
   'PYMODULE'),
  ('vine',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/vine/__init__.py',
   'PYMODULE'),
  ('vine.synchronization',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/vine/synchronization.py',
   'PYMODULE'),
  ('vine.promises',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/vine/promises.py',
   'PYMODULE'),
  ('vine.funtools',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/vine/funtools.py',
   'PYMODULE'),
  ('vine.abstract',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/vine/abstract.py',
   'PYMODULE'),
  ('kombu.utils.div',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/utils/div.py',
   'PYMODULE'),
  ('kombu.utils.collections',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/utils/collections.py',
   'PYMODULE'),
  ('kombu.utils.encoding',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/utils/encoding.py',
   'PYMODULE'),
  ('kombu.utils.compat',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/utils/compat.py',
   'PYMODULE'),
  ('kombu.exceptions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/exceptions.py',
   'PYMODULE'),
  ('kombu.asynchronous.http',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/asynchronous/http/__init__.py',
   'PYMODULE'),
  ('kombu.asynchronous.http.urllib3_client',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/asynchronous/http/urllib3_client.py',
   'PYMODULE'),
  ('kombu.asynchronous.hub',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/asynchronous/hub.py',
   'PYMODULE'),
  ('kombu.asynchronous.debug',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/asynchronous/debug.py',
   'PYMODULE'),
  ('kombu.asynchronous.timer',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/asynchronous/timer.py',
   'PYMODULE'),
  ('zoneinfo', '/usr/lib/python3.12/zoneinfo/__init__.py', 'PYMODULE'),
  ('zoneinfo._zoneinfo',
   '/usr/lib/python3.12/zoneinfo/_zoneinfo.py',
   'PYMODULE'),
  ('zoneinfo._common', '/usr/lib/python3.12/zoneinfo/_common.py', 'PYMODULE'),
  ('zoneinfo._tzpath', '/usr/lib/python3.12/zoneinfo/_tzpath.py', 'PYMODULE'),
  ('kombu.utils.eventio',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/utils/eventio.py',
   'PYMODULE'),
  ('kombu.log',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/log.py',
   'PYMODULE'),
  ('logging.handlers', '/usr/lib/python3.12/logging/handlers.py', 'PYMODULE'),
  ('smtplib', '/usr/lib/python3.12/smtplib.py', 'PYMODULE'),
  ('kombu.asynchronous.http.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/asynchronous/http/base.py',
   'PYMODULE'),
  ('kombu.asynchronous',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/asynchronous/__init__.py',
   'PYMODULE'),
  ('amqp',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/amqp/__init__.py',
   'PYMODULE'),
  ('amqp.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/amqp/utils.py',
   'PYMODULE'),
  ('amqp.exceptions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/amqp/exceptions.py',
   'PYMODULE'),
  ('amqp.connection',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/amqp/connection.py',
   'PYMODULE'),
  ('amqp.transport',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/amqp/transport.py',
   'PYMODULE'),
  ('amqp.platform',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/amqp/platform.py',
   'PYMODULE'),
  ('amqp.method_framing',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/amqp/method_framing.py',
   'PYMODULE'),
  ('amqp.abstract_channel',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/amqp/abstract_channel.py',
   'PYMODULE'),
  ('amqp.serialization',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/amqp/serialization.py',
   'PYMODULE'),
  ('amqp.spec',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/amqp/spec.py',
   'PYMODULE'),
  ('amqp.sasl',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/amqp/sasl.py',
   'PYMODULE'),
  ('amqp.channel',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/amqp/channel.py',
   'PYMODULE'),
  ('amqp.protocol',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/amqp/protocol.py',
   'PYMODULE'),
  ('amqp.basic_message',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/amqp/basic_message.py',
   'PYMODULE'),
  ('kombu.transport.memory',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/transport/memory.py',
   'PYMODULE'),
  ('kombu.transport.virtual',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/transport/virtual/__init__.py',
   'PYMODULE'),
  ('kombu.transport.virtual.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/transport/virtual/base.py',
   'PYMODULE'),
  ('kombu.transport.virtual.exchange',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/transport/virtual/exchange.py',
   'PYMODULE'),
  ('kombu.utils.text',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/utils/text.py',
   'PYMODULE'),
  ('kombu.utils.scheduling',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/utils/scheduling.py',
   'PYMODULE'),
  ('kombu.transport.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/transport/base.py',
   'PYMODULE'),
  ('kombu.messaging',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/messaging.py',
   'PYMODULE'),
  ('kombu.entity',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/entity.py',
   'PYMODULE'),
  ('kombu.abstract',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/abstract.py',
   'PYMODULE'),
  ('kombu.connection',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/connection.py',
   'PYMODULE'),
  ('kombu.simple',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/simple.py',
   'PYMODULE'),
  ('kombu.utils.debug',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/utils/debug.py',
   'PYMODULE'),
  ('kombu.utils.url',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/utils/url.py',
   'PYMODULE'),
  ('kombu.resource',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/resource.py',
   'PYMODULE'),
  ('kombu.compression',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/compression.py',
   'PYMODULE'),
  ('kombu.common',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/common.py',
   'PYMODULE'),
  ('kombu.utils.time',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/utils/time.py',
   'PYMODULE'),
  ('kombu.message',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/message.py',
   'PYMODULE'),
  ('kombu.transport',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/transport/__init__.py',
   'PYMODULE'),
  ('kombu',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/__init__.py',
   'PYMODULE'),
  ('kombu.pools',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/pools.py',
   'PYMODULE'),
  ('celery.concurrency.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/concurrency/base.py',
   'PYMODULE'),
  ('celery.utils.text',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/text.py',
   'PYMODULE'),
  ('celery.utils.log',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/log.py',
   'PYMODULE'),
  ('celery.utils.term',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/term.py',
   'PYMODULE'),
  ('celery.utils.timer2',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/timer2.py',
   'PYMODULE'),
  ('celery.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/__init__.py',
   'PYMODULE'),
  ('celery.utils.deprecated',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/deprecated.py',
   'PYMODULE'),
  ('celery.utils.abstract',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/abstract.py',
   'PYMODULE'),
  ('celery.utils.nodenames',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/nodenames.py',
   'PYMODULE'),
  ('celery.utils.imports',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/imports.py',
   'PYMODULE'),
  ('celery.utils.functional',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/functional.py',
   'PYMODULE'),
  ('celery.exceptions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/exceptions.py',
   'PYMODULE'),
  ('celery.utils.serialization',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/serialization.py',
   'PYMODULE'),
  ('celery.concurrency.solo',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/concurrency/solo.py',
   'PYMODULE'),
  ('celery.signals',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/signals.py',
   'PYMODULE'),
  ('celery.utils.dispatch',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/dispatch/__init__.py',
   'PYMODULE'),
  ('celery.utils.dispatch.signal',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/dispatch/signal.py',
   'PYMODULE'),
  ('celery.utils.time',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/time.py',
   'PYMODULE'),
  ('dateutil.tz',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/dateutil/tz/__init__.py',
   'PYMODULE'),
  ('dateutil.tz.tz',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/dateutil/tz/tz.py',
   'PYMODULE'),
  ('dateutil.zoneinfo',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/dateutil/zoneinfo/__init__.py',
   'PYMODULE'),
  ('dateutil.rrule',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/dateutil/rrule.py',
   'PYMODULE'),
  ('dateutil.easter',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/dateutil/easter.py',
   'PYMODULE'),
  ('dateutil._common',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/dateutil/_common.py',
   'PYMODULE'),
  ('dateutil.parser._parser',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/dateutil/parser/_parser.py',
   'PYMODULE'),
  ('dateutil.parser',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/dateutil/parser/__init__.py',
   'PYMODULE'),
  ('dateutil.parser.isoparser',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/dateutil/parser/isoparser.py',
   'PYMODULE'),
  ('dateutil.relativedelta',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/dateutil/relativedelta.py',
   'PYMODULE'),
  ('dateutil.tz.win',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/dateutil/tz/win.py',
   'PYMODULE'),
  ('dateutil.tz._factories',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/dateutil/tz/_factories.py',
   'PYMODULE'),
  ('dateutil.tz._common',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/dateutil/tz/_common.py',
   'PYMODULE'),
  ('six',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/six.py',
   'PYMODULE'),
  ('dateutil',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/dateutil/__init__.py',
   'PYMODULE'),
  ('dateutil._version',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/dateutil/_version.py',
   'PYMODULE'),
  ('celery.local',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/local.py',
   'PYMODULE'),
  ('celery.concurrency',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/concurrency/__init__.py',
   'PYMODULE'),
  ('celery.security',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/security/__init__.py',
   'PYMODULE'),
  ('celery.security.serialization',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/security/serialization.py',
   'PYMODULE'),
  ('celery.security.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/security/utils.py',
   'PYMODULE'),
  ('celery.security.key',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/security/key.py',
   'PYMODULE'),
  ('celery.security.certificate',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/security/certificate.py',
   'PYMODULE'),
  ('celery.app.defaults',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/app/defaults.py',
   'PYMODULE'),
  ('celery.loaders.app',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/loaders/app.py',
   'PYMODULE'),
  ('celery.loaders.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/loaders/base.py',
   'PYMODULE'),
  ('celery.utils.collections',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/collections.py',
   'PYMODULE'),
  ('celery.loaders',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/loaders/__init__.py',
   'PYMODULE'),
  ('celery.fixups.django',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/fixups/django.py',
   'PYMODULE'),
  ('celery.app.task',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/app/task.py',
   'PYMODULE'),
  ('celery.app.trace',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/app/trace.py',
   'PYMODULE'),
  ('celery.worker.state',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/worker/state.py',
   'PYMODULE'),
  ('celery.utils.debug',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/debug.py',
   'PYMODULE'),
  ('celery.platforms',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/platforms.py',
   'PYMODULE'),
  ('shelve', '/usr/lib/python3.12/shelve.py', 'PYMODULE'),
  ('dbm.gnu', '/usr/lib/python3.12/dbm/gnu.py', 'PYMODULE'),
  ('dbm.dumb', '/usr/lib/python3.12/dbm/dumb.py', 'PYMODULE'),
  ('dbm.ndbm', '/usr/lib/python3.12/dbm/ndbm.py', 'PYMODULE'),
  ('dbm', '/usr/lib/python3.12/dbm/__init__.py', 'PYMODULE'),
  ('celery.utils.saferepr',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/saferepr.py',
   'PYMODULE'),
  ('celery.utils.objects',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/objects.py',
   'PYMODULE'),
  ('celery.utils.threads',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/threads.py',
   'PYMODULE'),
  ('celery.app.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/app/utils.py',
   'PYMODULE'),
  ('celery.app.registry',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/app/registry.py',
   'PYMODULE'),
  ('celery.app.autoretry',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/app/autoretry.py',
   'PYMODULE'),
  ('celery.app.annotations',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/app/annotations.py',
   'PYMODULE'),
  ('celery.result',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/result.py',
   'PYMODULE'),
  ('celery.utils.graph',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/utils/graph.py',
   'PYMODULE'),
  ('celery.canvas',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/canvas.py',
   'PYMODULE'),
  ('celery.states',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/states.py',
   'PYMODULE'),
  ('celery.app.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/app/base.py',
   'PYMODULE'),
  ('celery.bin.celery',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bin/celery.py',
   'PYMODULE'),
  ('celery.bin',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bin/__init__.py',
   'PYMODULE'),
  ('celery.bin.worker',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bin/worker.py',
   'PYMODULE'),
  ('celery.bin.upgrade',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bin/upgrade.py',
   'PYMODULE'),
  ('celery.bin.shell',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bin/shell.py',
   'PYMODULE'),
  ('celery.bin.result',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bin/result.py',
   'PYMODULE'),
  ('celery.bin.purge',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bin/purge.py',
   'PYMODULE'),
  ('celery.bin.multi',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bin/multi.py',
   'PYMODULE'),
  ('celery.apps.multi',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/apps/multi.py',
   'PYMODULE'),
  ('celery.apps',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/apps/__init__.py',
   'PYMODULE'),
  ('celery.bin.migrate',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bin/migrate.py',
   'PYMODULE'),
  ('celery.contrib.migrate',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/contrib/migrate.py',
   'PYMODULE'),
  ('celery.contrib',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/contrib/__init__.py',
   'PYMODULE'),
  ('celery.bin.logtool',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bin/logtool.py',
   'PYMODULE'),
  ('fileinput', '/usr/lib/python3.12/fileinput.py', 'PYMODULE'),
  ('celery.bin.list',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bin/list.py',
   'PYMODULE'),
  ('celery.bin.graph',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bin/graph.py',
   'PYMODULE'),
  ('celery.bin.events',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bin/events.py',
   'PYMODULE'),
  ('celery.events.cursesmon',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/events/cursesmon.py',
   'PYMODULE'),
  ('celery.events',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/events/__init__.py',
   'PYMODULE'),
  ('celery.events.receiver',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/events/receiver.py',
   'PYMODULE'),
  ('kombu.mixins',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/mixins.py',
   'PYMODULE'),
  ('kombu.utils.limits',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/utils/limits.py',
   'PYMODULE'),
  ('celery.events.event',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/events/event.py',
   'PYMODULE'),
  ('celery.events.dispatcher',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/events/dispatcher.py',
   'PYMODULE'),
  ('celery.events.snapshot',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/events/snapshot.py',
   'PYMODULE'),
  ('celery.events.dumper',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/events/dumper.py',
   'PYMODULE'),
  ('celery.bin.control',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bin/control.py',
   'PYMODULE'),
  ('celery.worker.control',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/worker/control.py',
   'PYMODULE'),
  ('celery.worker.request',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/worker/request.py',
   'PYMODULE'),
  ('celery.bin.call',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bin/call.py',
   'PYMODULE'),
  ('celery.bin.beat',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bin/beat.py',
   'PYMODULE'),
  ('celery.bin.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bin/base.py',
   'PYMODULE'),
  ('celery.bin.amqp',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bin/amqp.py',
   'PYMODULE'),
  ('click_repl',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click_repl/__init__.py',
   'PYMODULE'),
  ('click_repl.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click_repl/utils.py',
   'PYMODULE'),
  ('click_repl.exceptions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click_repl/exceptions.py',
   'PYMODULE'),
  ('click_repl._repl',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click_repl/_repl.py',
   'PYMODULE'),
  ('prompt_toolkit.history',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/history.py',
   'PYMODULE'),
  ('asyncio', '/usr/lib/python3.12/asyncio/__init__.py', 'PYMODULE'),
  ('asyncio.unix_events',
   '/usr/lib/python3.12/asyncio/unix_events.py',
   'PYMODULE'),
  ('asyncio.log', '/usr/lib/python3.12/asyncio/log.py', 'PYMODULE'),
  ('asyncio.windows_events',
   '/usr/lib/python3.12/asyncio/windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   '/usr/lib/python3.12/asyncio/windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   '/usr/lib/python3.12/asyncio/selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   '/usr/lib/python3.12/asyncio/proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   '/usr/lib/python3.12/asyncio/base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads', '/usr/lib/python3.12/asyncio/threads.py', 'PYMODULE'),
  ('asyncio.taskgroups',
   '/usr/lib/python3.12/asyncio/taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   '/usr/lib/python3.12/asyncio/subprocess.py',
   'PYMODULE'),
  ('asyncio.streams', '/usr/lib/python3.12/asyncio/streams.py', 'PYMODULE'),
  ('asyncio.queues', '/usr/lib/python3.12/asyncio/queues.py', 'PYMODULE'),
  ('asyncio.runners', '/usr/lib/python3.12/asyncio/runners.py', 'PYMODULE'),
  ('asyncio.base_events',
   '/usr/lib/python3.12/asyncio/base_events.py',
   'PYMODULE'),
  ('asyncio.trsock', '/usr/lib/python3.12/asyncio/trsock.py', 'PYMODULE'),
  ('asyncio.staggered', '/usr/lib/python3.12/asyncio/staggered.py', 'PYMODULE'),
  ('asyncio.timeouts', '/usr/lib/python3.12/asyncio/timeouts.py', 'PYMODULE'),
  ('asyncio.tasks', '/usr/lib/python3.12/asyncio/tasks.py', 'PYMODULE'),
  ('asyncio.base_tasks',
   '/usr/lib/python3.12/asyncio/base_tasks.py',
   'PYMODULE'),
  ('asyncio.locks', '/usr/lib/python3.12/asyncio/locks.py', 'PYMODULE'),
  ('asyncio.mixins', '/usr/lib/python3.12/asyncio/mixins.py', 'PYMODULE'),
  ('asyncio.sslproto', '/usr/lib/python3.12/asyncio/sslproto.py', 'PYMODULE'),
  ('asyncio.transports',
   '/usr/lib/python3.12/asyncio/transports.py',
   'PYMODULE'),
  ('asyncio.protocols', '/usr/lib/python3.12/asyncio/protocols.py', 'PYMODULE'),
  ('asyncio.futures', '/usr/lib/python3.12/asyncio/futures.py', 'PYMODULE'),
  ('asyncio.base_futures',
   '/usr/lib/python3.12/asyncio/base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   '/usr/lib/python3.12/asyncio/exceptions.py',
   'PYMODULE'),
  ('asyncio.events', '/usr/lib/python3.12/asyncio/events.py', 'PYMODULE'),
  ('asyncio.format_helpers',
   '/usr/lib/python3.12/asyncio/format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   '/usr/lib/python3.12/asyncio/coroutines.py',
   'PYMODULE'),
  ('asyncio.constants', '/usr/lib/python3.12/asyncio/constants.py', 'PYMODULE'),
  ('prompt_toolkit',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.prompt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/shortcuts/prompt.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/formatted_text/base.py',
   'PYMODULE'),
  ('prompt_toolkit.mouse_events',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/mouse_events.py',
   'PYMODULE'),
  ('prompt_toolkit.data_structures',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/data_structures.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.toolbars',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/widgets/toolbars.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/widgets/__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.menus',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/widgets/menus.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.dialogs',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/widgets/dialogs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.focus',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/bindings/focus.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/bindings/__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.search',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/bindings/search.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.widgets.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/widgets/base.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.margins',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/layout/margins.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/formatted_text/utils.py',
   'PYMODULE'),
  ('prompt_toolkit.search',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/search.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.vi_state',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/vi_state.py',
   'PYMODULE'),
  ('prompt_toolkit.validation',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/validation.py',
   'PYMODULE'),
  ('prompt_toolkit.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/utils.py',
   'PYMODULE'),
  ('prompt_toolkit.output.windows10',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/output/windows10.py',
   'PYMODULE'),
  ('prompt_toolkit.output.win32',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/output/win32.py',
   'PYMODULE'),
  ('prompt_toolkit.output.vt100',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/output/vt100.py',
   'PYMODULE'),
  ('prompt_toolkit.output.flush_stdout',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/output/flush_stdout.py',
   'PYMODULE'),
  ('prompt_toolkit.output.color_depth',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/output/color_depth.py',
   'PYMODULE'),
  ('prompt_toolkit.output.defaults',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/output/defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.output.conemu',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/output/conemu.py',
   'PYMODULE'),
  ('prompt_toolkit.patch_stdout',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/patch_stdout.py',
   'PYMODULE'),
  ('prompt_toolkit.application.run_in_terminal',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/application/run_in_terminal.py',
   'PYMODULE'),
  ('prompt_toolkit.output.plain_text',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/output/plain_text.py',
   'PYMODULE'),
  ('prompt_toolkit.output.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/output/base.py',
   'PYMODULE'),
  ('prompt_toolkit.win32_types',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/win32_types.py',
   'PYMODULE'),
  ('wcwidth.wcwidth',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/wcwidth/wcwidth.py',
   'PYMODULE'),
  ('wcwidth.unicode_versions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/wcwidth/unicode_versions.py',
   'PYMODULE'),
  ('wcwidth.table_zero',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/wcwidth/table_zero.py',
   'PYMODULE'),
  ('wcwidth.table_wide',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/wcwidth/table_wide.py',
   'PYMODULE'),
  ('wcwidth.table_vs16',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/wcwidth/table_vs16.py',
   'PYMODULE'),
  ('wcwidth',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/wcwidth/__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.styles',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/styles/__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.style_transformation',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/styles/style_transformation.py',
   'PYMODULE'),
  ('prompt_toolkit.cache',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/cache.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.style',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/styles/style.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.pygments',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/styles/pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.named_colors',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/styles/named_colors.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.defaults',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/styles/defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.styles.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/styles/base.py',
   'PYMODULE'),
  ('prompt_toolkit.output',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/output/__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/lexers/__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers.pygments',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/lexers/pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.lexers.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/lexers/base.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/layout/utils.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.processors',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/layout/processors.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.menus',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/layout/menus.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.layout',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/layout/layout.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.dimension',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/layout/dimension.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.controls',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/layout/controls.py',
   'PYMODULE'),
  ('prompt_toolkit.selection',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/selection.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.containers',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/layout/containers.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.screen',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/layout/screen.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.mouse_handlers',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/layout/mouse_handlers.py',
   'PYMODULE'),
  ('prompt_toolkit.layout',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/layout/__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.scrollable_pane',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/layout/scrollable_pane.py',
   'PYMODULE'),
  ('prompt_toolkit.keys',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/keys.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.key_processor',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/key_processor.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.app',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/filters/app.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/filters/base.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.key_bindings',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/key_bindings.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.open_in_editor',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/bindings/open_in_editor.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.named_commands',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/bindings/named_commands.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.completion',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/bindings/completion.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.auto_suggest',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/bindings/auto_suggest.py',
   'PYMODULE'),
  ('prompt_toolkit.input.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/input/base.py',
   'PYMODULE'),
  ('prompt_toolkit.input',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/input/__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.input.defaults',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/input/defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.input.posix_pipe',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/input/posix_pipe.py',
   'PYMODULE'),
  ('prompt_toolkit.input.win32_pipe',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/input/win32_pipe.py',
   'PYMODULE'),
  ('prompt_toolkit.input.vt100_parser',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/input/vt100_parser.py',
   'PYMODULE'),
  ('prompt_toolkit.input.ansi_escape_sequences',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/input/ansi_escape_sequences.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.win32',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/eventloop/win32.py',
   'PYMODULE'),
  ('prompt_toolkit.input.vt100',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/input/vt100.py',
   'PYMODULE'),
  ('prompt_toolkit.input.posix_utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/input/posix_utils.py',
   'PYMODULE'),
  ('prompt_toolkit.input.win32',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/input/win32.py',
   'PYMODULE'),
  ('prompt_toolkit.filters',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/filters/__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/filters/utils.py',
   'PYMODULE'),
  ('prompt_toolkit.filters.cli',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/filters/cli.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/eventloop/__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/eventloop/utils.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.inputhook',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/eventloop/inputhook.py',
   'PYMODULE'),
  ('prompt_toolkit.eventloop.async_generator',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/eventloop/async_generator.py',
   'PYMODULE'),
  ('prompt_toolkit.enums',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/enums.py',
   'PYMODULE'),
  ('prompt_toolkit.document',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/document.py',
   'PYMODULE'),
  ('prompt_toolkit.cursor_shapes',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/cursor_shapes.py',
   'PYMODULE'),
  ('prompt_toolkit.completion',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/completion/__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.word_completer',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/completion/word_completer.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.nested',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/completion/nested.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.fuzzy_completer',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/completion/fuzzy_completer.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.filesystem',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/completion/filesystem.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.deduplicate',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/completion/deduplicate.py',
   'PYMODULE'),
  ('prompt_toolkit.completion.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/completion/base.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/clipboard/__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard.in_memory',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/clipboard/in_memory.py',
   'PYMODULE'),
  ('prompt_toolkit.clipboard.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/clipboard/base.py',
   'PYMODULE'),
  ('prompt_toolkit.buffer',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/buffer.py',
   'PYMODULE'),
  ('prompt_toolkit.auto_suggest',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/auto_suggest.py',
   'PYMODULE'),
  ('prompt_toolkit.application.current',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/application/current.py',
   'PYMODULE'),
  ('prompt_toolkit.application.dummy',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/application/dummy.py',
   'PYMODULE'),
  ('prompt_toolkit.application.application',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/application/application.py',
   'PYMODULE'),
  ('prompt_toolkit.renderer',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/renderer.py',
   'PYMODULE'),
  ('prompt_toolkit.layout.dummy',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/layout/dummy.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.emacs_state',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/emacs_state.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.defaults',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/defaults.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.vi',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/bindings/vi.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.digraphs',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/digraphs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.mouse',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/bindings/mouse.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.emacs',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/bindings/emacs.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.cpr',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/bindings/cpr.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.basic',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/bindings/basic.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.page_navigation',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/bindings/page_navigation.py',
   'PYMODULE'),
  ('prompt_toolkit.key_binding.bindings.scroll',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/key_binding/bindings/scroll.py',
   'PYMODULE'),
  ('prompt_toolkit.input.typeahead',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/input/typeahead.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/shortcuts/__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/shortcuts/utils.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/shortcuts/progress_bar/__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar.formatters',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/shortcuts/progress_bar/formatters.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.progress_bar.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/shortcuts/progress_bar/base.py',
   'PYMODULE'),
  ('prompt_toolkit.shortcuts.dialogs',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/shortcuts/dialogs.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/formatted_text/__init__.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.pygments',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/formatted_text/pygments.py',
   'PYMODULE'),
  ('prompt_toolkit.formatted_text.html',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/formatted_text/html.py',
   'PYMODULE'),
  ('xml.dom.minidom', '/usr/lib/python3.12/xml/dom/minidom.py', 'PYMODULE'),
  ('xml.dom.pulldom', '/usr/lib/python3.12/xml/dom/pulldom.py', 'PYMODULE'),
  ('xml.dom.expatbuilder',
   '/usr/lib/python3.12/xml/dom/expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   '/usr/lib/python3.12/xml/dom/NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   '/usr/lib/python3.12/xml/dom/xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   '/usr/lib/python3.12/xml/dom/minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg', '/usr/lib/python3.12/xml/dom/domreg.py', 'PYMODULE'),
  ('xml.dom', '/usr/lib/python3.12/xml/dom/__init__.py', 'PYMODULE'),
  ('prompt_toolkit.formatted_text.ansi',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/formatted_text/ansi.py',
   'PYMODULE'),
  ('prompt_toolkit.application',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit/application/__init__.py',
   'PYMODULE'),
  ('click_repl._completer',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click_repl/_completer.py',
   'PYMODULE'),
  ('click.shell_completion',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click/shell_completion.py',
   'PYMODULE'),
  ('click.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click/utils.py',
   'PYMODULE'),
  ('click.globals',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click/globals.py',
   'PYMODULE'),
  ('click._compat',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click/_compat.py',
   'PYMODULE'),
  ('click._winconsole',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click/_winconsole.py',
   'PYMODULE'),
  ('click.core',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click/core.py',
   'PYMODULE'),
  ('click.decorators',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click/decorators.py',
   'PYMODULE'),
  ('click.termui',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click/termui.py',
   'PYMODULE'),
  ('click._termui_impl',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click/_termui_impl.py',
   'PYMODULE'),
  ('click.parser',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click/parser.py',
   'PYMODULE'),
  ('click.formatting',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click/formatting.py',
   'PYMODULE'),
  ('click._textwrap',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click/_textwrap.py',
   'PYMODULE'),
  ('click_plugins',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click_plugins/__init__.py',
   'PYMODULE'),
  ('click_plugins.core',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click_plugins/core.py',
   'PYMODULE'),
  ('click_didyoumean',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click_didyoumean/__init__.py',
   'PYMODULE'),
  ('click.types',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click/types.py',
   'PYMODULE'),
  ('celery.app.builtins',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/app/builtins.py',
   'PYMODULE'),
  ('celery.app.backends',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/app/backends.py',
   'PYMODULE'),
  ('kombu.clocks',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/kombu/clocks.py',
   'PYMODULE'),
  ('click.exceptions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click/exceptions.py',
   'PYMODULE'),
  ('celery._state',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/_state.py',
   'PYMODULE'),
  ('celery.fixups',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/fixups/__init__.py',
   'PYMODULE'),
  ('celery.backends.cache',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/backends/cache.py',
   'PYMODULE'),
  ('celery.backends.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/backends/base.py',
   'PYMODULE'),
  ('celery.backends',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/backends/__init__.py',
   'PYMODULE'),
  ('celery.worker',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/worker/__init__.py',
   'PYMODULE'),
  ('celery.worker.worker',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/worker/worker.py',
   'PYMODULE'),
  ('celery.bootsteps',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/bootsteps.py',
   'PYMODULE'),
  ('celery.app',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/app/__init__.py',
   'PYMODULE'),
  ('celery',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/celery/__init__.py',
   'PYMODULE'),
  ('click',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click/__init__.py',
   'PYMODULE'),
  ('itsdangerous',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/itsdangerous/__init__.py',
   'PYMODULE'),
  ('itsdangerous.url_safe',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/itsdangerous/url_safe.py',
   'PYMODULE'),
  ('itsdangerous._json',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/itsdangerous/_json.py',
   'PYMODULE'),
  ('itsdangerous.timed',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/itsdangerous/timed.py',
   'PYMODULE'),
  ('itsdangerous.signer',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/itsdangerous/signer.py',
   'PYMODULE'),
  ('itsdangerous.serializer',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/itsdangerous/serializer.py',
   'PYMODULE'),
  ('itsdangerous.exc',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/itsdangerous/exc.py',
   'PYMODULE'),
  ('itsdangerous.encoding',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/itsdangerous/encoding.py',
   'PYMODULE'),
  ('markupsafe',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/markupsafe/__init__.py',
   'PYMODULE'),
  ('markupsafe._native',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/markupsafe/_native.py',
   'PYMODULE'),
  ('jinja2',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/__init__.py',
   'PYMODULE'),
  ('jinja2.ext',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/ext.py',
   'PYMODULE'),
  ('jinja2.parser',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/parser.py',
   'PYMODULE'),
  ('jinja2.lexer',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/lexer.py',
   'PYMODULE'),
  ('jinja2._identifier',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/_identifier.py',
   'PYMODULE'),
  ('jinja2.defaults',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/defaults.py',
   'PYMODULE'),
  ('jinja2.tests',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/tests.py',
   'PYMODULE'),
  ('jinja2.filters',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/filters.py',
   'PYMODULE'),
  ('jinja2.sandbox',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/sandbox.py',
   'PYMODULE'),
  ('jinja2.async_utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/async_utils.py',
   'PYMODULE'),
  ('jinja2.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/utils.py',
   'PYMODULE'),
  ('jinja2.constants',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/constants.py',
   'PYMODULE'),
  ('jinja2.runtime',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/runtime.py',
   'PYMODULE'),
  ('jinja2.loaders',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/loaders.py',
   'PYMODULE'),
  ('jinja2.exceptions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/exceptions.py',
   'PYMODULE'),
  ('jinja2.environment',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/environment.py',
   'PYMODULE'),
  ('jinja2.debug',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/debug.py',
   'PYMODULE'),
  ('jinja2.compiler',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/compiler.py',
   'PYMODULE'),
  ('jinja2.visitor',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/visitor.py',
   'PYMODULE'),
  ('jinja2.optimizer',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/optimizer.py',
   'PYMODULE'),
  ('jinja2.idtracking',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/idtracking.py',
   'PYMODULE'),
  ('jinja2.bccache',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/bccache.py',
   'PYMODULE'),
  ('jinja2.nodes',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/jinja2/nodes.py',
   'PYMODULE'),
  ('werkzeug',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/wrappers/__init__.py',
   'PYMODULE'),
  ('werkzeug.wrappers.response',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/wrappers/response.py',
   'PYMODULE'),
  ('werkzeug.wsgi',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/wsgi.py',
   'PYMODULE'),
  ('werkzeug.sansio.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/sansio/utils.py',
   'PYMODULE'),
  ('werkzeug.sansio',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/sansio/__init__.py',
   'PYMODULE'),
  ('werkzeug.sansio.http',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/sansio/http.py',
   'PYMODULE'),
  ('werkzeug.utils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/utils.py',
   'PYMODULE'),
  ('werkzeug.security',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/security.py',
   'PYMODULE'),
  ('werkzeug.urls',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/urls.py',
   'PYMODULE'),
  ('werkzeug.sansio.response',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/sansio/response.py',
   'PYMODULE'),
  ('werkzeug.datastructures.cache_control',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/datastructures/cache_control.py',
   'PYMODULE'),
  ('werkzeug.datastructures.structures',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/datastructures/structures.py',
   'PYMODULE'),
  ('werkzeug.datastructures.mixins',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/datastructures/mixins.py',
   'PYMODULE'),
  ('werkzeug.http',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/http.py',
   'PYMODULE'),
  ('werkzeug.datastructures',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/datastructures/__init__.py',
   'PYMODULE'),
  ('werkzeug.datastructures.range',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/datastructures/range.py',
   'PYMODULE'),
  ('werkzeug.datastructures.headers',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/datastructures/headers.py',
   'PYMODULE'),
  ('werkzeug.datastructures.file_storage',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/datastructures/file_storage.py',
   'PYMODULE'),
  ('werkzeug.datastructures.etag',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/datastructures/etag.py',
   'PYMODULE'),
  ('werkzeug.datastructures.csp',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/datastructures/csp.py',
   'PYMODULE'),
  ('werkzeug.datastructures.auth',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/datastructures/auth.py',
   'PYMODULE'),
  ('werkzeug.datastructures.accept',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/datastructures/accept.py',
   'PYMODULE'),
  ('werkzeug._internal',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/_internal.py',
   'PYMODULE'),
  ('werkzeug.wrappers.request',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/wrappers/request.py',
   'PYMODULE'),
  ('werkzeug.sansio.request',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/sansio/request.py',
   'PYMODULE'),
  ('werkzeug.user_agent',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/user_agent.py',
   'PYMODULE'),
  ('werkzeug.formparser',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/formparser.py',
   'PYMODULE'),
  ('werkzeug.sansio.multipart',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/sansio/multipart.py',
   'PYMODULE'),
  ('werkzeug.test',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/test.py',
   'PYMODULE'),
  ('werkzeug.serving',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/serving.py',
   'PYMODULE'),
  ('werkzeug._reloader',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/_reloader.py',
   'PYMODULE'),
  ('werkzeug.debug',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/debug/__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.console',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/debug/console.py',
   'PYMODULE'),
  ('werkzeug.debug.repr',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/debug/repr.py',
   'PYMODULE'),
  ('werkzeug.middleware.shared_data',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/middleware/shared_data.py',
   'PYMODULE'),
  ('werkzeug.middleware',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/middleware/__init__.py',
   'PYMODULE'),
  ('werkzeug.debug.tbtools',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/debug/tbtools.py',
   'PYMODULE'),
  ('werkzeug.exceptions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/exceptions.py',
   'PYMODULE'),
  ('tracemalloc', '/usr/lib/python3.12/tracemalloc.py', 'PYMODULE'),
  ('stringprep', '/usr/lib/python3.12/stringprep.py', 'PYMODULE'),
  ('_py_abc', '/usr/lib/python3.12/_py_abc.py', 'PYMODULE'),
  ('numpy',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/__init__.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/ctypeslib.py',
   'PYMODULE'),
  ('numpy.random',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/polynomial/__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/polynomial/_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/polynomial/laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/polynomial/hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/polynomial/hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/polynomial/legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/polynomial/chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/polynomial/polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/polynomial/polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/fft/__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/fft/helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/fft/_pocketfft.py',
   'PYMODULE'),
  ('numpy.dtypes',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/dtypes.py',
   'PYMODULE'),
  ('numpy.__config__',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/_distributor_init.py',
   'PYMODULE'),
  ('uuid', '/usr/lib/python3.12/uuid.py', 'PYMODULE'),
  ('utils.validation',
   '/home/<USER>/projects/homework/backend/utils/validation.py',
   'PYMODULE'),
  ('routes.thumbnail',
   '/home/<USER>/projects/homework/backend/routes/thumbnail.py',
   'PYMODULE'),
  ('routes.system',
   '/home/<USER>/projects/homework/backend/routes/system.py',
   'PYMODULE'),
  ('routes.download',
   '/home/<USER>/projects/homework/backend/routes/download.py',
   'PYMODULE'),
  ('routes.preview',
   '/home/<USER>/projects/homework/backend/routes/preview.py',
   'PYMODULE'),
  ('routes.upload',
   '/home/<USER>/projects/homework/backend/routes/upload.py',
   'PYMODULE'),
  ('config', '/home/<USER>/projects/homework/backend/config.py', 'PYMODULE'),
  ('flask_cors',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask_cors/__init__.py',
   'PYMODULE'),
  ('flask_cors.version',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask_cors/version.py',
   'PYMODULE'),
  ('flask_cors.extension',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask_cors/extension.py',
   'PYMODULE'),
  ('flask_cors.core',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask_cors/core.py',
   'PYMODULE'),
  ('flask_cors.decorator',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask_cors/decorator.py',
   'PYMODULE'),
  ('flask',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/__init__.py',
   'PYMODULE'),
  ('flask.templating',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/templating.py',
   'PYMODULE'),
  ('flask.debughelpers',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/debughelpers.py',
   'PYMODULE'),
  ('flask.scaffold',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/scaffold.py',
   'PYMODULE'),
  ('flask.wrappers',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/wrappers.py',
   'PYMODULE'),
  ('werkzeug.routing',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/routing/__init__.py',
   'PYMODULE'),
  ('werkzeug.routing.rules',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/routing/rules.py',
   'PYMODULE'),
  ('werkzeug.routing.matcher',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/routing/matcher.py',
   'PYMODULE'),
  ('werkzeug.routing.map',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/routing/map.py',
   'PYMODULE'),
  ('werkzeug.routing.exceptions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/routing/exceptions.py',
   'PYMODULE'),
  ('werkzeug.routing.converters',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/routing/converters.py',
   'PYMODULE'),
  ('flask.signals',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/signals.py',
   'PYMODULE'),
  ('blinker',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/blinker/__init__.py',
   'PYMODULE'),
  ('blinker.base',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/blinker/base.py',
   'PYMODULE'),
  ('blinker._utilities',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/blinker/_utilities.py',
   'PYMODULE'),
  ('flask.helpers',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/helpers.py',
   'PYMODULE'),
  ('flask.globals',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/globals.py',
   'PYMODULE'),
  ('flask.sessions',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/sessions.py',
   'PYMODULE'),
  ('flask.json.tag',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/json/tag.py',
   'PYMODULE'),
  ('werkzeug.local',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug/local.py',
   'PYMODULE'),
  ('flask.ctx',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/ctx.py',
   'PYMODULE'),
  ('flask.config',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/config.py',
   'PYMODULE'),
  ('flask.blueprints',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/blueprints.py',
   'PYMODULE'),
  ('flask.app',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/app.py',
   'PYMODULE'),
  ('flask.testing',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/testing.py',
   'PYMODULE'),
  ('click.testing',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click/testing.py',
   'PYMODULE'),
  ('flask.logging',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/logging.py',
   'PYMODULE'),
  ('flask.json.provider',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/json/provider.py',
   'PYMODULE'),
  ('flask.cli',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/cli.py',
   'PYMODULE'),
  ('flask.typing',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/typing.py',
   'PYMODULE'),
  ('flask.json',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/json/__init__.py',
   'PYMODULE'),
  ('socket', '/usr/lib/python3.12/socket.py', 'PYMODULE'),
  ('logging', '/usr/lib/python3.12/logging/__init__.py', 'PYMODULE')],
 [('imageio_ffmpeg/binaries/ffmpeg-linux-x86_64-v7.0.2',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio_ffmpeg/binaries/ffmpeg-linux-x86_64-v7.0.2',
   'BINARY'),
  ('libpython3.12.so', '/usr/lib/x86_64-linux-gnu/libpython3.12.so', 'BINARY'),
  ('cv2/python-3.12/cv2.cpython-312-x86_64-linux-gnu.so',
   '/usr/local/lib/python3.12/dist-packages/cv2/python-3.12/cv2.cpython-312-x86_64-linux-gnu.so',
   'BINARY'),
  ('lib-dynload/_contextvars.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_contextvars.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_decimal.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_hashlib.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_lzma.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_bz2.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/resource.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_queue.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_queue.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/mmap.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/mmap.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_posixshmem.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_posixshmem.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_ctypes.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_ctypes.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_multiprocessing.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_multiprocessing.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/termios.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/termios.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_ssl.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_ssl.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/readline.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/readline.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_json.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('psutil/_psutil_posix.abi3.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/psutil/_psutil_posix.abi3.so',
   'EXTENSION'),
  ('psutil/_psutil_linux.abi3.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/psutil/_psutil_linux.abi3.so',
   'EXTENSION'),
  ('lib-dynload/_curses.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_curses.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/core/_multiarray_umath.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/_multiarray_umath.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/linalg/_umath_linalg.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/linalg/_umath_linalg.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_imaging.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/_imaging.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_imagingmath.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/_imagingmath.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('charset_normalizer/md__mypyc.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/charset_normalizer/md__mypyc.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_multibytecodec.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('charset_normalizer/md.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/charset_normalizer/md.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_webp.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/_webp.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_imagingtk.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/_imagingtk.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_avif.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/_avif.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('PIL/_imagingcms.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/PIL/_imagingcms.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_zoneinfo.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_zoneinfo.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_gdbm.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_gdbm.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_dbm.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_dbm.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_asyncio.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_asyncio.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('markupsafe/_speedups.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/markupsafe/_speedups.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_codecs_jp.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_codecs_kr.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_codecs_iso2022.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_codecs_cn.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_codecs_tw.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-312-x86_64-linux-gnu.so',
   '/usr/lib/python3.12/lib-dynload/_codecs_hk.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/core/_multiarray_tests.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/core/_multiarray_tests.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/random/mtrand.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/mtrand.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/random/_sfc64.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/_sfc64.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/random/_philox.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/_philox.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/random/_pcg64.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/_pcg64.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/random/_mt19937.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/_mt19937.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/random/bit_generator.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/bit_generator.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/random/_generator.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/_generator.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/random/_bounded_integers.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/_bounded_integers.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/random/_common.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/random/_common.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('numpy/fft/_pocketfft_internal.cpython-312-x86_64-linux-gnu.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy/fft/_pocketfft_internal.cpython-312-x86_64-linux-gnu.so',
   'EXTENSION'),
  ('libexpat.so.1', '/usr/lib/x86_64-linux-gnu/libexpat.so.1', 'BINARY'),
  ('libz.so.1', '/usr/lib/x86_64-linux-gnu/libz.so.1', 'BINARY'),
  ('libpcre2-8.so.0', '/usr/lib/x86_64-linux-gnu/libpcre2-8.so.0', 'BINARY'),
  ('libxcb-render.so.0',
   '/usr/lib/x86_64-linux-gnu/libxcb-render.so.0',
   'BINARY'),
  ('libopencore-amrnb.so.0',
   '/usr/lib/x86_64-linux-gnu/libopencore-amrnb.so.0',
   'BINARY'),
  ('libva-drm.so.2', '/usr/lib/x86_64-linux-gnu/libva-drm.so.2', 'BINARY'),
  ('libhogweed.so.6', '/usr/lib/x86_64-linux-gnu/libhogweed.so.6', 'BINARY'),
  ('libgssapi_krb5.so.2',
   '/usr/lib/x86_64-linux-gnu/libgssapi_krb5.so.2',
   'BINARY'),
  ('libopencv_saliency.so.413',
   '/usr/local/lib/libopencv_saliency.so.413',
   'BINARY'),
  ('libopencv_cudaoptflow.so.413',
   '/usr/local/lib/libopencv_cudaoptflow.so.413',
   'BINARY'),
  ('libjpeg.so.8', '/usr/lib/x86_64-linux-gnu/libjpeg.so.8', 'BINARY'),
  ('libwebp.so.7', '/usr/lib/x86_64-linux-gnu/libwebp.so.7', 'BINARY'),
  ('libXrender.so.1', '/usr/lib/x86_64-linux-gnu/libXrender.so.1', 'BINARY'),
  ('librav1e.so.0', '/usr/lib/x86_64-linux-gnu/librav1e.so.0', 'BINARY'),
  ('libxml2.so.2', '/usr/lib/x86_64-linux-gnu/libxml2.so.2', 'BINARY'),
  ('libepoxy.so.0', '/usr/lib/x86_64-linux-gnu/libepoxy.so.0', 'BINARY'),
  ('libfreetype.so.6', '/usr/lib/x86_64-linux-gnu/libfreetype.so.6', 'BINARY'),
  ('libopencv_features2d.so.413',
   '/usr/local/lib/libopencv_features2d.so.413',
   'BINARY'),
  ('libvo-amrwbenc.so.0',
   '/usr/lib/x86_64-linux-gnu/libvo-amrwbenc.so.0',
   'BINARY'),
  ('libogg.so.0', '/usr/lib/x86_64-linux-gnu/libogg.so.0', 'BINARY'),
  ('libopencv_reg.so.413', '/usr/local/lib/libopencv_reg.so.413', 'BINARY'),
  ('libvorbis.so.0', '/usr/lib/x86_64-linux-gnu/libvorbis.so.0', 'BINARY'),
  ('libunwind.so.8', '/usr/lib/x86_64-linux-gnu/libunwind.so.8', 'BINARY'),
  ('libopencv_highgui.so.413',
   '/usr/local/lib/libopencv_highgui.so.413',
   'BINARY'),
  ('libvdpau.so.1', '/usr/lib/x86_64-linux-gnu/libvdpau.so.1', 'BINARY'),
  ('liblcms2.so.2', '/usr/lib/x86_64-linux-gnu/liblcms2.so.2', 'BINARY'),
  ('libopencv_cudabgsegm.so.413',
   '/usr/local/lib/libopencv_cudabgsegm.so.413',
   'BINARY'),
  ('libva.so.2', '/usr/lib/x86_64-linux-gnu/libva.so.2', 'BINARY'),
  ('libnppitc.so.12', '/usr/lib/x86_64-linux-gnu/libnppitc.so.12', 'BINARY'),
  ('libbz2.so.1.0', '/usr/lib/x86_64-linux-gnu/libbz2.so.1.0', 'BINARY'),
  ('libopencv_rgbd.so.413', '/usr/local/lib/libopencv_rgbd.so.413', 'BINARY'),
  ('libopencv_cudafilters.so.413',
   '/usr/local/lib/libopencv_cudafilters.so.413',
   'BINARY'),
  ('libsoxr.so.0', '/usr/lib/x86_64-linux-gnu/libsoxr.so.0', 'BINARY'),
  ('libelf.so.1', '/usr/lib/x86_64-linux-gnu/libelf.so.1', 'BINARY'),
  ('libopencv_rapid.so.413', '/usr/local/lib/libopencv_rapid.so.413', 'BINARY'),
  ('libmpg123.so.0', '/usr/lib/x86_64-linux-gnu/libmpg123.so.0', 'BINARY'),
  ('libicudata.so.74', '/usr/lib/x86_64-linux-gnu/libicudata.so.74', 'BINARY'),
  ('libzstd.so.1', '/usr/lib/x86_64-linux-gnu/libzstd.so.1', 'BINARY'),
  ('libatspi.so.0', '/usr/lib/x86_64-linux-gnu/libatspi.so.0', 'BINARY'),
  ('libpgm-5.3.so.0', '/usr/lib/x86_64-linux-gnu/libpgm-5.3.so.0', 'BINARY'),
  ('libshine.so.3', '/usr/lib/x86_64-linux-gnu/libshine.so.3', 'BINARY'),
  ('libcom_err.so.2', '/usr/lib/x86_64-linux-gnu/libcom_err.so.2', 'BINARY'),
  ('libmp3lame.so.0', '/usr/lib/x86_64-linux-gnu/libmp3lame.so.0', 'BINARY'),
  ('libcrypto.so.3', '/usr/lib/x86_64-linux-gnu/libcrypto.so.3', 'BINARY'),
  ('libXi.so.6', '/usr/lib/x86_64-linux-gnu/libXi.so.6', 'BINARY'),
  ('libopencv_photo.so.413', '/usr/local/lib/libopencv_photo.so.413', 'BINARY'),
  ('libXext.so.6', '/usr/lib/x86_64-linux-gnu/libXext.so.6', 'BINARY'),
  ('libcairo.so.2', '/usr/lib/x86_64-linux-gnu/libcairo.so.2', 'BINARY'),
  ('libswresample.so.4',
   '/usr/lib/x86_64-linux-gnu/libswresample.so.4',
   'BINARY'),
  ('libx265.so.199', '/usr/lib/x86_64-linux-gnu/libx265.so.199', 'BINARY'),
  ('libopencv_ccalib.so.413',
   '/usr/local/lib/libopencv_ccalib.so.413',
   'BINARY'),
  ('libopencore-amrwb.so.0',
   '/usr/lib/x86_64-linux-gnu/libopencore-amrwb.so.0',
   'BINARY'),
  ('libgobject-2.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgobject-2.0.so.0',
   'BINARY'),
  ('libbrotlienc.so.1',
   '/usr/lib/x86_64-linux-gnu/libbrotlienc.so.1',
   'BINARY'),
  ('libgdk_pixbuf-2.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgdk_pixbuf-2.0.so.0',
   'BINARY'),
  ('libSvtAv1Enc.so.1',
   '/usr/lib/x86_64-linux-gnu/libSvtAv1Enc.so.1',
   'BINARY'),
  ('libX11-xcb.so.1', '/usr/lib/x86_64-linux-gnu/libX11-xcb.so.1', 'BINARY'),
  ('libXcomposite.so.1',
   '/usr/lib/x86_64-linux-gnu/libXcomposite.so.1',
   'BINARY'),
  ('libopencv_freetype.so.413',
   '/usr/local/lib/libopencv_freetype.so.413',
   'BINARY'),
  ('libcublasLt.so.12',
   '/usr/lib/x86_64-linux-gnu/libcublasLt.so.12',
   'BINARY'),
  ('libicuuc.so.74', '/usr/lib/x86_64-linux-gnu/libicuuc.so.74', 'BINARY'),
  ('libopencv_flann.so.413', '/usr/local/lib/libopencv_flann.so.413', 'BINARY'),
  ('libopencv_ml.so.413', '/usr/local/lib/libopencv_ml.so.413', 'BINARY'),
  ('libgsm.so.1', '/usr/lib/x86_64-linux-gnu/libgsm.so.1', 'BINARY'),
  ('libopencv_dnn_superres.so.413',
   '/usr/local/lib/libopencv_dnn_superres.so.413',
   'BINARY'),
  ('libpango-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libpango-1.0.so.0',
   'BINARY'),
  ('libblkid.so.1', '/usr/lib/x86_64-linux-gnu/libblkid.so.1', 'BINARY'),
  ('libtiff.so.6', '/usr/lib/x86_64-linux-gnu/libtiff.so.6', 'BINARY'),
  ('libatk-bridge-2.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libatk-bridge-2.0.so.0',
   'BINARY'),
  ('libopencv_stereo.so.413',
   '/usr/local/lib/libopencv_stereo.so.413',
   'BINARY'),
  ('libopencv_plot.so.413', '/usr/local/lib/libopencv_plot.so.413', 'BINARY'),
  ('libopencv_xfeatures2d.so.413',
   '/usr/local/lib/libopencv_xfeatures2d.so.413',
   'BINARY'),
  ('libIex-3_1.so.30', '/usr/lib/x86_64-linux-gnu/libIex-3_1.so.30', 'BINARY'),
  ('libopencv_bgsegm.so.413',
   '/usr/local/lib/libopencv_bgsegm.so.413',
   'BINARY'),
  ('libgstaudio-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgstaudio-1.0.so.0',
   'BINARY'),
  ('libsodium.so.23', '/usr/lib/x86_64-linux-gnu/libsodium.so.23', 'BINARY'),
  ('libnorm.so.1', '/usr/lib/x86_64-linux-gnu/libnorm.so.1', 'BINARY'),
  ('libpixman-1.so.0', '/usr/lib/x86_64-linux-gnu/libpixman-1.so.0', 'BINARY'),
  ('libgdk-3.so.0', '/usr/lib/x86_64-linux-gnu/libgdk-3.so.0', 'BINARY'),
  ('libopencv_videoio.so.413',
   '/usr/local/lib/libopencv_videoio.so.413',
   'BINARY'),
  ('libnppig.so.12', '/usr/lib/x86_64-linux-gnu/libnppig.so.12', 'BINARY'),
  ('libnppif.so.12', '/usr/lib/x86_64-linux-gnu/libnppif.so.12', 'BINARY'),
  ('libchromaprint.so.1',
   '/usr/lib/x86_64-linux-gnu/libchromaprint.so.1',
   'BINARY'),
  ('libXdmcp.so.6', '/usr/lib/x86_64-linux-gnu/libXdmcp.so.6', 'BINARY'),
  ('libnppial.so.12', '/usr/lib/x86_64-linux-gnu/libnppial.so.12', 'BINARY'),
  ('libtheoraenc.so.1',
   '/usr/lib/x86_64-linux-gnu/libtheoraenc.so.1',
   'BINARY'),
  ('libXfixes.so.3', '/usr/lib/x86_64-linux-gnu/libXfixes.so.3', 'BINARY'),
  ('libwebpmux.so.3', '/usr/lib/x86_64-linux-gnu/libwebpmux.so.3', 'BINARY'),
  ('liblz4.so.1', '/usr/lib/x86_64-linux-gnu/liblz4.so.1', 'BINARY'),
  ('libOpenCL.so.1', '/usr/lib/x86_64-linux-gnu/libOpenCL.so.1', 'BINARY'),
  ('libgmp.so.10', '/usr/lib/x86_64-linux-gnu/libgmp.so.10', 'BINARY'),
  ('libdbus-1.so.3', '/usr/lib/x86_64-linux-gnu/libdbus-1.so.3', 'BINARY'),
  ('libcudnn.so.9', '/usr/lib/x86_64-linux-gnu/libcudnn.so.9', 'BINARY'),
  ('libIlmThread-3_1.so.30',
   '/usr/lib/x86_64-linux-gnu/libIlmThread-3_1.so.30',
   'BINARY'),
  ('libsrt-gnutls.so.1.5',
   '/usr/lib/x86_64-linux-gnu/libsrt-gnutls.so.1.5',
   'BINARY'),
  ('libnppidei.so.12', '/usr/lib/x86_64-linux-gnu/libnppidei.so.12', 'BINARY'),
  ('libvpx.so.9', '/usr/lib/x86_64-linux-gnu/libvpx.so.9', 'BINARY'),
  ('libwebpdemux.so.2',
   '/usr/lib/x86_64-linux-gnu/libwebpdemux.so.2',
   'BINARY'),
  ('libgmodule-2.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgmodule-2.0.so.0',
   'BINARY'),
  ('libopencv_gapi.so.413', '/usr/local/lib/libopencv_gapi.so.413', 'BINARY'),
  ('libvpl.so.2', '/usr/lib/x86_64-linux-gnu/libvpl.so.2', 'BINARY'),
  ('librabbitmq.so.4', '/usr/lib/x86_64-linux-gnu/librabbitmq.so.4', 'BINARY'),
  ('libmbedcrypto.so.7',
   '/usr/lib/x86_64-linux-gnu/libmbedcrypto.so.7',
   'BINARY'),
  ('libdeflate.so.0', '/usr/lib/x86_64-linux-gnu/libdeflate.so.0', 'BINARY'),
  ('libnppc.so.12', '/usr/lib/x86_64-linux-gnu/libnppc.so.12', 'BINARY'),
  ('libopus.so.0', '/usr/lib/x86_64-linux-gnu/libopus.so.0', 'BINARY'),
  ('libdatrie.so.1', '/usr/lib/x86_64-linux-gnu/libdatrie.so.1', 'BINARY'),
  ('libopencv_signal.so.413',
   '/usr/local/lib/libopencv_signal.so.413',
   'BINARY'),
  ('libxcb-shm.so.0', '/usr/lib/x86_64-linux-gnu/libxcb-shm.so.0', 'BINARY'),
  ('libmount.so.1', '/usr/lib/x86_64-linux-gnu/libmount.so.1', 'BINARY'),
  ('libXcursor.so.1', '/usr/lib/x86_64-linux-gnu/libXcursor.so.1', 'BINARY'),
  ('libvorbisfile.so.3',
   '/usr/lib/x86_64-linux-gnu/libvorbisfile.so.3',
   'BINARY'),
  ('libopencv_fuzzy.so.413', '/usr/local/lib/libopencv_fuzzy.so.413', 'BINARY'),
  ('libaribb24.so.0', '/usr/lib/x86_64-linux-gnu/libaribb24.so.0', 'BINARY'),
  ('libhwy.so.1', '/usr/lib/x86_64-linux-gnu/libhwy.so.1', 'BINARY'),
  ('libudfread.so.0', '/usr/lib/x86_64-linux-gnu/libudfread.so.0', 'BINARY'),
  ('libgstbase-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgstbase-1.0.so.0',
   'BINARY'),
  ('libopencv_phase_unwrapping.so.413',
   '/usr/local/lib/libopencv_phase_unwrapping.so.413',
   'BINARY'),
  ('libssl.so.3', '/usr/lib/x86_64-linux-gnu/libssl.so.3', 'BINARY'),
  ('libgsttag-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgsttag-1.0.so.0',
   'BINARY'),
  ('libk5crypto.so.3', '/usr/lib/x86_64-linux-gnu/libk5crypto.so.3', 'BINARY'),
  ('libgstreamer-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgstreamer-1.0.so.0',
   'BINARY'),
  ('libopencv_surface_matching.so.413',
   '/usr/local/lib/libopencv_surface_matching.so.413',
   'BINARY'),
  ('libavcodec.so.60', '/usr/lib/x86_64-linux-gnu/libavcodec.so.60', 'BINARY'),
  ('libcap.so.2', '/usr/lib/x86_64-linux-gnu/libcap.so.2', 'BINARY'),
  ('libopencv_stitching.so.413',
   '/usr/local/lib/libopencv_stitching.so.413',
   'BINARY'),
  ('libfribidi.so.0', '/usr/lib/x86_64-linux-gnu/libfribidi.so.0', 'BINARY'),
  ('libgstapp-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgstapp-1.0.so.0',
   'BINARY'),
  ('libtwolame.so.0', '/usr/lib/x86_64-linux-gnu/libtwolame.so.0', 'BINARY'),
  ('libaom.so.3', '/usr/lib/x86_64-linux-gnu/libaom.so.3', 'BINARY'),
  ('libgme.so.0', '/usr/lib/x86_64-linux-gnu/libgme.so.0', 'BINARY'),
  ('libopencv_ximgproc.so.413',
   '/usr/local/lib/libopencv_ximgproc.so.413',
   'BINARY'),
  ('libopencv_imgproc.so.413',
   '/usr/local/lib/libopencv_imgproc.so.413',
   'BINARY'),
  ('libpangocairo-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libpangocairo-1.0.so.0',
   'BINARY'),
  ('libxkbcommon.so.0',
   '/usr/lib/x86_64-linux-gnu/libxkbcommon.so.0',
   'BINARY'),
  ('libopenmpt.so.0', '/usr/lib/x86_64-linux-gnu/libopenmpt.so.0', 'BINARY'),
  ('liblzma.so.5', '/usr/lib/x86_64-linux-gnu/liblzma.so.5', 'BINARY'),
  ('libLerc.so.4', '/usr/lib/x86_64-linux-gnu/libLerc.so.4', 'BINARY'),
  ('libopencv_cudacodec.so.413',
   '/usr/local/lib/libopencv_cudacodec.so.413',
   'BINARY'),
  ('libp11-kit.so.0', '/usr/lib/x86_64-linux-gnu/libp11-kit.so.0', 'BINARY'),
  ('libdav1d.so.7', '/usr/lib/x86_64-linux-gnu/libdav1d.so.7', 'BINARY'),
  ('libopencv_cudaobjdetect.so.413',
   '/usr/local/lib/libopencv_cudaobjdetect.so.413',
   'BINARY'),
  ('libopencv_quality.so.413',
   '/usr/local/lib/libopencv_quality.so.413',
   'BINARY'),
  ('libvorbisenc.so.2',
   '/usr/lib/x86_64-linux-gnu/libvorbisenc.so.2',
   'BINARY'),
  ('librsvg-2.so.2', '/usr/lib/x86_64-linux-gnu/librsvg-2.so.2', 'BINARY'),
  ('libpangoft2-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libpangoft2-1.0.so.0',
   'BINARY'),
  ('libopencv_objdetect.so.413',
   '/usr/local/lib/libopencv_objdetect.so.413',
   'BINARY'),
  ('libswscale.so.7', '/usr/lib/x86_64-linux-gnu/libswscale.so.7', 'BINARY'),
  ('libopencv_mcc.so.413', '/usr/local/lib/libopencv_mcc.so.413', 'BINARY'),
  ('libavformat.so.60',
   '/usr/lib/x86_64-linux-gnu/libavformat.so.60',
   'BINARY'),
  ('libopencv_img_hash.so.413',
   '/usr/local/lib/libopencv_img_hash.so.413',
   'BINARY'),
  ('libfontconfig.so.1',
   '/usr/lib/x86_64-linux-gnu/libfontconfig.so.1',
   'BINARY'),
  ('libnppicc.so.12', '/usr/lib/x86_64-linux-gnu/libnppicc.so.12', 'BINARY'),
  ('libnuma.so.1', '/usr/lib/x86_64-linux-gnu/libnuma.so.1', 'BINARY'),
  ('libopencv_tracking.so.413',
   '/usr/local/lib/libopencv_tracking.so.413',
   'BINARY'),
  ('libopencv_face.so.413', '/usr/local/lib/libopencv_face.so.413', 'BINARY'),
  ('libavutil.so.58', '/usr/lib/x86_64-linux-gnu/libavutil.so.58', 'BINARY'),
  ('libnppist.so.12', '/usr/lib/x86_64-linux-gnu/libnppist.so.12', 'BINARY'),
  ('libzmq.so.5', '/usr/lib/x86_64-linux-gnu/libzmq.so.5', 'BINARY'),
  ('libstdc++.so.6', '/usr/lib/x86_64-linux-gnu/libstdc++.so.6', 'BINARY'),
  ('libopenjp2.so.7', '/usr/lib/x86_64-linux-gnu/libopenjp2.so.7', 'BINARY'),
  ('libglib-2.0.so.0', '/usr/lib/x86_64-linux-gnu/libglib-2.0.so.0', 'BINARY'),
  ('libjxl.so.0.7', '/usr/lib/x86_64-linux-gnu/libjxl.so.0.7', 'BINARY'),
  ('libharfbuzz.so.0', '/usr/lib/x86_64-linux-gnu/libharfbuzz.so.0', 'BINARY'),
  ('libx264.so.164', '/usr/lib/x86_64-linux-gnu/libx264.so.164', 'BINARY'),
  ('libgpg-error.so.0',
   '/usr/lib/x86_64-linux-gnu/libgpg-error.so.0',
   'BINARY'),
  ('libgtk-3.so.0', '/usr/lib/x86_64-linux-gnu/libgtk-3.so.0', 'BINARY'),
  ('libopencv_cudafeatures2d.so.413',
   '/usr/local/lib/libopencv_cudafeatures2d.so.413',
   'BINARY'),
  ('libgstriff-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgstriff-1.0.so.0',
   'BINARY'),
  ('libX11.so.6', '/usr/lib/x86_64-linux-gnu/libX11.so.6', 'BINARY'),
  ('libnettle.so.8', '/usr/lib/x86_64-linux-gnu/libnettle.so.8', 'BINARY'),
  ('libcublas.so.12', '/usr/lib/x86_64-linux-gnu/libcublas.so.12', 'BINARY'),
  ('libatk-1.0.so.0', '/usr/lib/x86_64-linux-gnu/libatk-1.0.so.0', 'BINARY'),
  ('libcodec2.so.1.2', '/usr/lib/x86_64-linux-gnu/libcodec2.so.1.2', 'BINARY'),
  ('libva-x11.so.2', '/usr/lib/x86_64-linux-gnu/libva-x11.so.2', 'BINARY'),
  ('libopencv_structured_light.so.413',
   '/usr/local/lib/libopencv_structured_light.so.413',
   'BINARY'),
  ('libjxl_threads.so.0.7',
   '/usr/lib/x86_64-linux-gnu/libjxl_threads.so.0.7',
   'BINARY'),
  ('liborc-0.4.so.0', '/usr/lib/x86_64-linux-gnu/liborc-0.4.so.0', 'BINARY'),
  ('librist.so.4', '/usr/lib/x86_64-linux-gnu/librist.so.4', 'BINARY'),
  ('libkrb5.so.3', '/usr/lib/x86_64-linux-gnu/libkrb5.so.3', 'BINARY'),
  ('libopencv_core.so.413', '/usr/local/lib/libopencv_core.so.413', 'BINARY'),
  ('libdw.so.1', '/usr/lib/x86_64-linux-gnu/libdw.so.1', 'BINARY'),
  ('libidn2.so.0', '/usr/lib/x86_64-linux-gnu/libidn2.so.0', 'BINARY'),
  ('libopencv_optflow.so.413',
   '/usr/local/lib/libopencv_optflow.so.413',
   'BINARY'),
  ('libopencv_wechat_qrcode.so.413',
   '/usr/local/lib/libopencv_wechat_qrcode.so.413',
   'BINARY'),
  ('libgomp.so.1', '/usr/lib/x86_64-linux-gnu/libgomp.so.1', 'BINARY'),
  ('libopencv_imgcodecs.so.413',
   '/usr/local/lib/libopencv_imgcodecs.so.413',
   'BINARY'),
  ('libXdamage.so.1', '/usr/lib/x86_64-linux-gnu/libXdamage.so.1', 'BINARY'),
  ('libXinerama.so.1', '/usr/lib/x86_64-linux-gnu/libXinerama.so.1', 'BINARY'),
  ('libbrotlidec.so.1',
   '/usr/lib/x86_64-linux-gnu/libbrotlidec.so.1',
   'BINARY'),
  ('libopencv_bioinspired.so.413',
   '/usr/local/lib/libopencv_bioinspired.so.413',
   'BINARY'),
  ('libopencv_dnn.so.413', '/usr/local/lib/libopencv_dnn.so.413', 'BINARY'),
  ('libgcc_s.so.1', '/usr/lib/x86_64-linux-gnu/libgcc_s.so.1', 'BINARY'),
  ('libcairo-gobject.so.2',
   '/usr/lib/x86_64-linux-gnu/libcairo-gobject.so.2',
   'BINARY'),
  ('libcjson.so.1', '/usr/lib/x86_64-linux-gnu/libcjson.so.1', 'BINARY'),
  ('libbsd.so.0', '/usr/lib/x86_64-linux-gnu/libbsd.so.0', 'BINARY'),
  ('libnppim.so.12', '/usr/lib/x86_64-linux-gnu/libnppim.so.12', 'BINARY'),
  ('libopencv_intensity_transform.so.413',
   '/usr/local/lib/libopencv_intensity_transform.so.413',
   'BINARY'),
  ('libopencv_line_descriptor.so.413',
   '/usr/local/lib/libopencv_line_descriptor.so.413',
   'BINARY'),
  ('libxvidcore.so.4', '/usr/lib/x86_64-linux-gnu/libxvidcore.so.4', 'BINARY'),
  ('libsnappy.so.1', '/usr/lib/x86_64-linux-gnu/libsnappy.so.1', 'BINARY'),
  ('libopencv_cudawarping.so.413',
   '/usr/local/lib/libopencv_cudawarping.so.413',
   'BINARY'),
  ('libsharpyuv.so.0', '/usr/lib/x86_64-linux-gnu/libsharpyuv.so.0', 'BINARY'),
  ('libthai.so.0', '/usr/lib/x86_64-linux-gnu/libthai.so.0', 'BINARY'),
  ('libjbig.so.0', '/usr/lib/x86_64-linux-gnu/libjbig.so.0', 'BINARY'),
  ('libtheoradec.so.1',
   '/usr/lib/x86_64-linux-gnu/libtheoradec.so.1',
   'BINARY'),
  ('libzvbi.so.0', '/usr/lib/x86_64-linux-gnu/libzvbi.so.0', 'BINARY'),
  ('libopencv_hfs.so.413', '/usr/local/lib/libopencv_hfs.so.413', 'BINARY'),
  ('libspeex.so.1', '/usr/lib/x86_64-linux-gnu/libspeex.so.1', 'BINARY'),
  ('libopencv_cudaarithm.so.413',
   '/usr/local/lib/libopencv_cudaarithm.so.413',
   'BINARY'),
  ('libselinux.so.1', '/usr/lib/x86_64-linux-gnu/libselinux.so.1', 'BINARY'),
  ('libpng16.so.16', '/usr/lib/x86_64-linux-gnu/libpng16.so.16', 'BINARY'),
  ('libmd.so.0', '/usr/lib/x86_64-linux-gnu/libmd.so.0', 'BINARY'),
  ('libkeyutils.so.1', '/usr/lib/x86_64-linux-gnu/libkeyutils.so.1', 'BINARY'),
  ('libopencv_aruco.so.413', '/usr/local/lib/libopencv_aruco.so.413', 'BINARY'),
  ('libsystemd.so.0', '/usr/lib/x86_64-linux-gnu/libsystemd.so.0', 'BINARY'),
  ('libcufft.so.11', '/usr/lib/x86_64-linux-gnu/libcufft.so.11', 'BINARY'),
  ('libffi.so.8', '/usr/lib/x86_64-linux-gnu/libffi.so.8', 'BINARY'),
  ('libunistring.so.5',
   '/usr/lib/x86_64-linux-gnu/libunistring.so.5',
   'BINARY'),
  ('libopencv_xphoto.so.413',
   '/usr/local/lib/libopencv_xphoto.so.413',
   'BINARY'),
  ('libbluray.so.2', '/usr/lib/x86_64-linux-gnu/libbluray.so.2', 'BINARY'),
  ('libkrb5support.so.0',
   '/usr/lib/x86_64-linux-gnu/libkrb5support.so.0',
   'BINARY'),
  ('libXrandr.so.2', '/usr/lib/x86_64-linux-gnu/libXrandr.so.2', 'BINARY'),
  ('libopencv_video.so.413', '/usr/local/lib/libopencv_video.so.413', 'BINARY'),
  ('libbrotlicommon.so.1',
   '/usr/lib/x86_64-linux-gnu/libbrotlicommon.so.1',
   'BINARY'),
  ('libImath-3_1.so.29',
   '/usr/lib/x86_64-linux-gnu/libImath-3_1.so.29',
   'BINARY'),
  ('libXau.so.6', '/usr/lib/x86_64-linux-gnu/libXau.so.6', 'BINARY'),
  ('libopencv_text.so.413', '/usr/local/lib/libopencv_text.so.413', 'BINARY'),
  ('libopencv_cudastereo.so.413',
   '/usr/local/lib/libopencv_cudastereo.so.413',
   'BINARY'),
  ('libopencv_cudalegacy.so.413',
   '/usr/local/lib/libopencv_cudalegacy.so.413',
   'BINARY'),
  ('libgstvideo-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgstvideo-1.0.so.0',
   'BINARY'),
  ('libtasn1.so.6', '/usr/lib/x86_64-linux-gnu/libtasn1.so.6', 'BINARY'),
  ('libgcrypt.so.20', '/usr/lib/x86_64-linux-gnu/libgcrypt.so.20', 'BINARY'),
  ('libssh-gcrypt.so.4',
   '/usr/lib/x86_64-linux-gnu/libssh-gcrypt.so.4',
   'BINARY'),
  ('libgio-2.0.so.0', '/usr/lib/x86_64-linux-gnu/libgio-2.0.so.0', 'BINARY'),
  ('libopencv_cudaimgproc.so.413',
   '/usr/local/lib/libopencv_cudaimgproc.so.413',
   'BINARY'),
  ('libopencv_shape.so.413', '/usr/local/lib/libopencv_shape.so.413', 'BINARY'),
  ('libgnutls.so.30', '/usr/lib/x86_64-linux-gnu/libgnutls.so.30', 'BINARY'),
  ('libgraphite2.so.3',
   '/usr/lib/x86_64-linux-gnu/libgraphite2.so.3',
   'BINARY'),
  ('libgstpbutils-1.0.so.0',
   '/usr/lib/x86_64-linux-gnu/libgstpbutils-1.0.so.0',
   'BINARY'),
  ('libopencv_calib3d.so.413',
   '/usr/local/lib/libopencv_calib3d.so.413',
   'BINARY'),
  ('libOpenEXR-3_1.so.30',
   '/usr/lib/x86_64-linux-gnu/libOpenEXR-3_1.so.30',
   'BINARY'),
  ('libreadline.so.8', '/usr/lib/x86_64-linux-gnu/libreadline.so.8', 'BINARY'),
  ('libtinfo.so.6', '/usr/lib/x86_64-linux-gnu/libtinfo.so.6', 'BINARY'),
  ('libncursesw.so.6', '/usr/lib/x86_64-linux-gnu/libncursesw.so.6', 'BINARY'),
  ('numpy.libs/libquadmath-96973f99.so.0.0.0',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy.libs/libquadmath-96973f99.so.0.0.0',
   'BINARY'),
  ('numpy.libs/libopenblas64_p-r0-0cf96a72.3.23.dev.so',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy.libs/libopenblas64_p-r0-0cf96a72.3.23.dev.so',
   'BINARY'),
  ('numpy.libs/libgfortran-040039e1.so.5.0.0',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/numpy.libs/libgfortran-040039e1.so.5.0.0',
   'BINARY'),
  ('pillow.libs/libXau-154567c4.so.6.0.0',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libXau-154567c4.so.6.0.0',
   'BINARY'),
  ('pillow.libs/libxcb-64009ff3.so.1.1.0',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libxcb-64009ff3.so.1.1.0',
   'BINARY'),
  ('pillow.libs/liblzma-64b7ab39.so.5.8.1',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/liblzma-64b7ab39.so.5.8.1',
   'BINARY'),
  ('pillow.libs/libjpeg-8a13c6e0.so.62.4.0',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libjpeg-8a13c6e0.so.62.4.0',
   'BINARY'),
  ('pillow.libs/libtiff-13a02c81.so.6.1.0',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libtiff-13a02c81.so.6.1.0',
   'BINARY'),
  ('pillow.libs/libopenjp2-56811f71.so.2.5.3',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libopenjp2-56811f71.so.2.5.3',
   'BINARY'),
  ('pillow.libs/libwebpmux-6f2b1ad9.so.3.1.1',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libwebpmux-6f2b1ad9.so.3.1.1',
   'BINARY'),
  ('pillow.libs/libwebp-5f0275c0.so.7.1.10',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libwebp-5f0275c0.so.7.1.10',
   'BINARY'),
  ('pillow.libs/libsharpyuv-60a7c00b.so.0.1.1',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libsharpyuv-60a7c00b.so.0.1.1',
   'BINARY'),
  ('pillow.libs/libwebpdemux-efaed568.so.2.0.16',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libwebpdemux-efaed568.so.2.0.16',
   'BINARY'),
  ('pillow.libs/libavif-01e67780.so.16.3.0',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/libavif-01e67780.so.16.3.0',
   'BINARY'),
  ('pillow.libs/liblcms2-cc10e42f.so.2.0.17',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/pillow.libs/liblcms2-cc10e42f.so.2.0.17',
   'BINARY'),
  ('libgdbm.so.6', '/usr/lib/x86_64-linux-gnu/libgdbm.so.6', 'BINARY'),
  ('libdb-5.3.so', '/usr/lib/x86_64-linux-gnu/libdb-5.3.so', 'BINARY')],
 [],
 [],
 [('cv2/Error/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/Error/__init__.pyi',
   'DATA'),
  ('cv2/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/__init__.pyi',
   'DATA'),
  ('cv2/aruco/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/aruco/__init__.pyi',
   'DATA'),
  ('cv2/barcode/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/barcode/__init__.pyi',
   'DATA'),
  ('cv2/bgsegm/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/bgsegm/__init__.pyi',
   'DATA'),
  ('cv2/bioinspired/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/bioinspired/__init__.pyi',
   'DATA'),
  ('cv2/ccm/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ccm/__init__.pyi',
   'DATA'),
  ('cv2/colored_kinfu/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/colored_kinfu/__init__.pyi',
   'DATA'),
  ('cv2/cuda/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/cuda/__init__.pyi',
   'DATA'),
  ('cv2/cudacodec/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/cudacodec/__init__.pyi',
   'DATA'),
  ('cv2/datasets/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/datasets/__init__.pyi',
   'DATA'),
  ('cv2/detail/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/detail/__init__.pyi',
   'DATA'),
  ('cv2/dnn/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/dnn/__init__.pyi',
   'DATA'),
  ('cv2/dnn_superres/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/dnn_superres/__init__.pyi',
   'DATA'),
  ('cv2/dpm/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/dpm/__init__.pyi',
   'DATA'),
  ('cv2/dynafu/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/dynafu/__init__.pyi',
   'DATA'),
  ('cv2/face/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/face/__init__.pyi',
   'DATA'),
  ('cv2/fisheye/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/fisheye/__init__.pyi',
   'DATA'),
  ('cv2/flann/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/flann/__init__.pyi',
   'DATA'),
  ('cv2/freetype/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/freetype/__init__.pyi',
   'DATA'),
  ('cv2/ft/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ft/__init__.pyi',
   'DATA'),
  ('cv2/gapi/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/__init__.pyi',
   'DATA'),
  ('cv2/gapi/core/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/core/__init__.pyi',
   'DATA'),
  ('cv2/gapi/core/cpu/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/core/cpu/__init__.pyi',
   'DATA'),
  ('cv2/gapi/core/fluid/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/core/fluid/__init__.pyi',
   'DATA'),
  ('cv2/gapi/core/ocl/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/core/ocl/__init__.pyi',
   'DATA'),
  ('cv2/gapi/ie/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/ie/__init__.pyi',
   'DATA'),
  ('cv2/gapi/ie/detail/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/ie/detail/__init__.pyi',
   'DATA'),
  ('cv2/gapi/imgproc/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/imgproc/__init__.pyi',
   'DATA'),
  ('cv2/gapi/imgproc/fluid/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/imgproc/fluid/__init__.pyi',
   'DATA'),
  ('cv2/gapi/oak/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/oak/__init__.pyi',
   'DATA'),
  ('cv2/gapi/onnx/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/onnx/__init__.pyi',
   'DATA'),
  ('cv2/gapi/onnx/ep/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/onnx/ep/__init__.pyi',
   'DATA'),
  ('cv2/gapi/ot/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/ot/__init__.pyi',
   'DATA'),
  ('cv2/gapi/ot/cpu/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/ot/cpu/__init__.pyi',
   'DATA'),
  ('cv2/gapi/ov/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/ov/__init__.pyi',
   'DATA'),
  ('cv2/gapi/own/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/own/__init__.pyi',
   'DATA'),
  ('cv2/gapi/own/detail/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/own/detail/__init__.pyi',
   'DATA'),
  ('cv2/gapi/render/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/render/__init__.pyi',
   'DATA'),
  ('cv2/gapi/render/ocv/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/render/ocv/__init__.pyi',
   'DATA'),
  ('cv2/gapi/streaming/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/streaming/__init__.pyi',
   'DATA'),
  ('cv2/gapi/video/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/video/__init__.pyi',
   'DATA'),
  ('cv2/gapi/wip/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/wip/__init__.pyi',
   'DATA'),
  ('cv2/gapi/wip/draw/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/wip/draw/__init__.pyi',
   'DATA'),
  ('cv2/gapi/wip/gst/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/wip/gst/__init__.pyi',
   'DATA'),
  ('cv2/gapi/wip/onevpl/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/wip/onevpl/__init__.pyi',
   'DATA'),
  ('cv2/hfs/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/hfs/__init__.pyi',
   'DATA'),
  ('cv2/img_hash/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/img_hash/__init__.pyi',
   'DATA'),
  ('cv2/intensity_transform/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/intensity_transform/__init__.pyi',
   'DATA'),
  ('cv2/ipp/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ipp/__init__.pyi',
   'DATA'),
  ('cv2/kinfu/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/kinfu/__init__.pyi',
   'DATA'),
  ('cv2/kinfu/detail/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/kinfu/detail/__init__.pyi',
   'DATA'),
  ('cv2/large_kinfu/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/large_kinfu/__init__.pyi',
   'DATA'),
  ('cv2/legacy/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/legacy/__init__.pyi',
   'DATA'),
  ('cv2/line_descriptor/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/line_descriptor/__init__.pyi',
   'DATA'),
  ('cv2/linemod/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/linemod/__init__.pyi',
   'DATA'),
  ('cv2/mcc/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/mcc/__init__.pyi',
   'DATA'),
  ('cv2/ml/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ml/__init__.pyi',
   'DATA'),
  ('cv2/motempl/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/motempl/__init__.pyi',
   'DATA'),
  ('cv2/multicalib/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/multicalib/__init__.pyi',
   'DATA'),
  ('cv2/ocl/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ocl/__init__.pyi',
   'DATA'),
  ('cv2/ogl/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ogl/__init__.pyi',
   'DATA'),
  ('cv2/omnidir/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/omnidir/__init__.pyi',
   'DATA'),
  ('cv2/optflow/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/optflow/__init__.pyi',
   'DATA'),
  ('cv2/parallel/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/parallel/__init__.pyi',
   'DATA'),
  ('cv2/phase_unwrapping/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/phase_unwrapping/__init__.pyi',
   'DATA'),
  ('cv2/plot/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/plot/__init__.pyi',
   'DATA'),
  ('cv2/ppf_match_3d/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ppf_match_3d/__init__.pyi',
   'DATA'),
  ('cv2/py.typed',
   '/usr/local/lib/python3.12/dist-packages/cv2/py.typed',
   'DATA'),
  ('cv2/quality/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/quality/__init__.pyi',
   'DATA'),
  ('cv2/rapid/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/rapid/__init__.pyi',
   'DATA'),
  ('cv2/reg/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/reg/__init__.pyi',
   'DATA'),
  ('cv2/rgbd/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/rgbd/__init__.pyi',
   'DATA'),
  ('cv2/saliency/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/saliency/__init__.pyi',
   'DATA'),
  ('cv2/samples/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/samples/__init__.pyi',
   'DATA'),
  ('cv2/segmentation/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/segmentation/__init__.pyi',
   'DATA'),
  ('cv2/signal/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/signal/__init__.pyi',
   'DATA'),
  ('cv2/stereo/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/stereo/__init__.pyi',
   'DATA'),
  ('cv2/structured_light/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/structured_light/__init__.pyi',
   'DATA'),
  ('cv2/text/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/text/__init__.pyi',
   'DATA'),
  ('cv2/utils/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/utils/__init__.pyi',
   'DATA'),
  ('cv2/utils/fs/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/utils/fs/__init__.pyi',
   'DATA'),
  ('cv2/utils/nested/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/utils/nested/__init__.pyi',
   'DATA'),
  ('cv2/videoio_registry/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/videoio_registry/__init__.pyi',
   'DATA'),
  ('cv2/videostab/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/videostab/__init__.pyi',
   'DATA'),
  ('cv2/wechat_qrcode/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/wechat_qrcode/__init__.pyi',
   'DATA'),
  ('cv2/xfeatures2d/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/xfeatures2d/__init__.pyi',
   'DATA'),
  ('cv2/ximgproc/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ximgproc/__init__.pyi',
   'DATA'),
  ('cv2/ximgproc/segmentation/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/ximgproc/segmentation/__init__.pyi',
   'DATA'),
  ('cv2/xphoto/__init__.pyi',
   '/usr/local/lib/python3.12/dist-packages/cv2/xphoto/__init__.pyi',
   'DATA'),
  ('flask/py.typed',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask/py.typed',
   'DATA'),
  ('routes/__init__.py',
   '/home/<USER>/projects/homework/backend/routes/__init__.py',
   'DATA'),
  ('routes/__pycache__/__init__.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/__init__.cpython-312.pyc',
   'DATA'),
  ('routes/__pycache__/download.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/download.cpython-312.pyc',
   'DATA'),
  ('routes/__pycache__/preview.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/preview.cpython-312.pyc',
   'DATA'),
  ('routes/__pycache__/process.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/process.cpython-312.pyc',
   'DATA'),
  ('routes/__pycache__/system.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/system.cpython-312.pyc',
   'DATA'),
  ('routes/__pycache__/thumbnail.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/thumbnail.cpython-312.pyc',
   'DATA'),
  ('routes/__pycache__/upload.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/routes/__pycache__/upload.cpython-312.pyc',
   'DATA'),
  ('routes/download.py',
   '/home/<USER>/projects/homework/backend/routes/download.py',
   'DATA'),
  ('routes/preview.py',
   '/home/<USER>/projects/homework/backend/routes/preview.py',
   'DATA'),
  ('routes/process.py',
   '/home/<USER>/projects/homework/backend/routes/process.py',
   'DATA'),
  ('routes/system.py',
   '/home/<USER>/projects/homework/backend/routes/system.py',
   'DATA'),
  ('routes/thumbnail.py',
   '/home/<USER>/projects/homework/backend/routes/thumbnail.py',
   'DATA'),
  ('routes/upload.py',
   '/home/<USER>/projects/homework/backend/routes/upload.py',
   'DATA'),
  ('tasks/__init__.py',
   '/home/<USER>/projects/homework/backend/tasks/__init__.py',
   'DATA'),
  ('tasks/__pycache__/__init__.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/__init__.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/advanced_image_processing.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/advanced_image_processing.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/beauty_processing.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/beauty_processing.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/ffmpeg_video_processor.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/ffmpeg_video_processor.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/image_filters.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/image_filters.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/optimized_video_processor.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/optimized_video_processor.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/parallel_video_processor.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/parallel_video_processor.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/pure_opencv_ffmpeg_processor.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/pure_opencv_ffmpeg_processor.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/truly_optimized_video_processor.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/truly_optimized_video_processor.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/video_processor.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/video_processor.cpython-39.pyc',
   'DATA'),
  ('tasks/__pycache__/video_tasks.cpython-39.pyc',
   '/home/<USER>/projects/homework/backend/tasks/__pycache__/video_tasks.cpython-39.pyc',
   'DATA'),
  ('tasks/beauty_processing.py',
   '/home/<USER>/projects/homework/backend/tasks/beauty_processing.py',
   'DATA'),
  ('tasks/image_edge_detection.py',
   '/home/<USER>/projects/homework/backend/tasks/image_edge_detection.py',
   'DATA'),
  ('tasks/image_fusion.py',
   '/home/<USER>/projects/homework/backend/tasks/image_fusion.py',
   'DATA'),
  ('tasks/image_gamma_correction.py',
   '/home/<USER>/projects/homework/backend/tasks/image_gamma_correction.py',
   'DATA'),
  ('tasks/image_grayscale.py',
   '/home/<USER>/projects/homework/backend/tasks/image_grayscale.py',
   'DATA'),
  ('tasks/image_sharpen.py',
   '/home/<USER>/projects/homework/backend/tasks/image_sharpen.py',
   'DATA'),
  ('tasks/image_stitching.py',
   '/home/<USER>/projects/homework/backend/tasks/image_stitching.py',
   'DATA'),
  ('tasks/opencv_ffmpeg_processor.py',
   '/home/<USER>/projects/homework/backend/tasks/opencv_ffmpeg_processor.py',
   'DATA'),
  ('tasks/texture_transfer.py',
   '/home/<USER>/projects/homework/backend/tasks/texture_transfer.py',
   'DATA'),
  ('tasks/video_binary.py',
   '/home/<USER>/projects/homework/backend/tasks/video_binary.py',
   'DATA'),
  ('tasks/video_blur.py',
   '/home/<USER>/projects/homework/backend/tasks/video_blur.py',
   'DATA'),
  ('tasks/video_edge_detection.py',
   '/home/<USER>/projects/homework/backend/tasks/video_edge_detection.py',
   'DATA'),
  ('tasks/video_extract_frame.py',
   '/home/<USER>/projects/homework/backend/tasks/video_extract_frame.py',
   'DATA'),
  ('tasks/video_grayscale.py',
   '/home/<USER>/projects/homework/backend/tasks/video_grayscale.py',
   'DATA'),
  ('tasks/video_processing/__init__.py',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/__init__.py',
   'DATA'),
  ('tasks/video_processing/__pycache__/__init__.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/__pycache__/__init__.cpython-312.pyc',
   'DATA'),
  ('tasks/video_processing/__pycache__/ffmpeg_processor.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/__pycache__/ffmpeg_processor.cpython-312.pyc',
   'DATA'),
  ('tasks/video_processing/__pycache__/opencv_processors.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/__pycache__/opencv_processors.cpython-312.pyc',
   'DATA'),
  ('tasks/video_processing/__pycache__/performance_config.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/__pycache__/performance_config.cpython-312.pyc',
   'DATA'),
  ('tasks/video_processing/ffmpeg_processor.py',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/ffmpeg_processor.py',
   'DATA'),
  ('tasks/video_processing/opencv_processors.py',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/opencv_processors.py',
   'DATA'),
  ('tasks/video_processing/performance_config.py',
   '/home/<USER>/projects/homework/backend/tasks/video_processing/performance_config.py',
   'DATA'),
  ('tasks/video_resize.py',
   '/home/<USER>/projects/homework/backend/tasks/video_resize.py',
   'DATA'),
  ('tasks/video_tasks.py',
   '/home/<USER>/projects/homework/backend/tasks/video_tasks.py',
   'DATA'),
  ('tasks/video_thumbnail.py',
   '/home/<USER>/projects/homework/backend/tasks/video_thumbnail.py',
   'DATA'),
  ('tasks/video_transform.py',
   '/home/<USER>/projects/homework/backend/tasks/video_transform.py',
   'DATA'),
  ('utils/__init__.py',
   '/home/<USER>/projects/homework/backend/utils/__init__.py',
   'DATA'),
  ('utils/__pycache__/__init__.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/utils/__pycache__/__init__.cpython-312.pyc',
   'DATA'),
  ('utils/__pycache__/cache_manager.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/utils/__pycache__/cache_manager.cpython-312.pyc',
   'DATA'),
  ('utils/__pycache__/validation.cpython-312.pyc',
   '/home/<USER>/projects/homework/backend/utils/__pycache__/validation.cpython-312.pyc',
   'DATA'),
  ('utils/cache_manager.py',
   '/home/<USER>/projects/homework/backend/utils/cache_manager.py',
   'DATA'),
  ('utils/validation.py',
   '/home/<USER>/projects/homework/backend/utils/validation.py',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/INSTALLER',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/LICENSE',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/METADATA',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/top_level.txt',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/WHEEL',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/REQUESTED',
   'DATA'),
  ('setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/importlib_metadata-8.0.0.dist-info/RECORD',
   'DATA'),
  ('setuptools/_vendor/jaraco/text/Lorem ipsum.txt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/setuptools/_vendor/jaraco/text/Lorem '
   'ipsum.txt',
   'DATA'),
  ('imageio_ffmpeg/binaries/README.md',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/imageio_ffmpeg/binaries/README.md',
   'DATA'),
  ('certifi/py.typed',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/certifi/py.typed',
   'DATA'),
  ('certifi/cacert.pem',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/certifi/cacert.pem',
   'DATA'),
  ('dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/dateutil/zoneinfo/dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('cv2/config.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/config.py',
   'DATA'),
  ('cv2/config-3.12.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/config-3.12.py',
   'DATA'),
  ('cv2/load_config_py3.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/load_config_py3.py',
   'DATA'),
  ('prompt_toolkit-3.0.51.dist-info/top_level.txt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit-3.0.51.dist-info/top_level.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info/WHEEL',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug-3.1.3.dist-info/WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info/RECORD',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/itsdangerous-2.2.0.dist-info/RECORD',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info/METADATA',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/itsdangerous-2.2.0.dist-info/METADATA',
   'DATA'),
  ('click-8.2.1.dist-info/METADATA',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click-8.2.1.dist-info/METADATA',
   'DATA'),
  ('flask-2.3.3.dist-info/entry_points.txt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask-2.3.3.dist-info/entry_points.txt',
   'DATA'),
  ('werkzeug-3.1.3.dist-info/METADATA',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug-3.1.3.dist-info/METADATA',
   'DATA'),
  ('flask-2.3.3.dist-info/LICENSE.rst',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask-2.3.3.dist-info/LICENSE.rst',
   'DATA'),
  ('prompt_toolkit-3.0.51.dist-info/WHEEL',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit-3.0.51.dist-info/WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info/LICENSE.txt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/itsdangerous-2.2.0.dist-info/LICENSE.txt',
   'DATA'),
  ('prompt_toolkit-3.0.51.dist-info/licenses/LICENSE',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit-3.0.51.dist-info/licenses/LICENSE',
   'DATA'),
  ('werkzeug-3.1.3.dist-info/INSTALLER',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug-3.1.3.dist-info/INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info/RECORD',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click-8.2.1.dist-info/RECORD',
   'DATA'),
  ('prompt_toolkit-3.0.51.dist-info/METADATA',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit-3.0.51.dist-info/METADATA',
   'DATA'),
  ('flask-2.3.3.dist-info/INSTALLER',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask-2.3.3.dist-info/INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info/INSTALLER',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/itsdangerous-2.2.0.dist-info/INSTALLER',
   'DATA'),
  ('flask-2.3.3.dist-info/REQUESTED',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask-2.3.3.dist-info/REQUESTED',
   'DATA'),
  ('prompt_toolkit-3.0.51.dist-info/RECORD',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit-3.0.51.dist-info/RECORD',
   'DATA'),
  ('flask-2.3.3.dist-info/RECORD',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask-2.3.3.dist-info/RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/LICENSE.txt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/INSTALLER',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info/WHEEL',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click-8.2.1.dist-info/WHEEL',
   'DATA'),
  ('prompt_toolkit-3.0.51.dist-info/licenses/AUTHORS.rst',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit-3.0.51.dist-info/licenses/AUTHORS.rst',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/WHEEL',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/RECORD',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/RECORD',
   'DATA'),
  ('click-8.2.1.dist-info/INSTALLER',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click-8.2.1.dist-info/INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info/WHEEL',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/itsdangerous-2.2.0.dist-info/WHEEL',
   'DATA'),
  ('werkzeug-3.1.3.dist-info/LICENSE.txt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug-3.1.3.dist-info/LICENSE.txt',
   'DATA'),
  ('flask-2.3.3.dist-info/WHEEL',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask-2.3.3.dist-info/WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info/licenses/LICENSE.txt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/click-8.2.1.dist-info/licenses/LICENSE.txt',
   'DATA'),
  ('prompt_toolkit-3.0.51.dist-info/INSTALLER',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/prompt_toolkit-3.0.51.dist-info/INSTALLER',
   'DATA'),
  ('werkzeug-3.1.3.dist-info/RECORD',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/werkzeug-3.1.3.dist-info/RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/METADATA',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info/top_level.txt',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/MarkupSafe-3.0.2.dist-info/top_level.txt',
   'DATA'),
  ('flask-2.3.3.dist-info/METADATA',
   '/home/<USER>/projects/homework/backend/.venv/lib/python3.12/site-packages/flask-2.3.3.dist-info/METADATA',
   'DATA'),
  ('cv2/__init__.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/__init__.py',
   'DATA'),
  ('cv2/utils/__init__.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/utils/__init__.py',
   'DATA'),
  ('cv2/typing/__init__.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/typing/__init__.py',
   'DATA'),
  ('cv2/misc/version.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/misc/version.py',
   'DATA'),
  ('cv2/misc/__init__.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/misc/__init__.py',
   'DATA'),
  ('cv2/mat_wrapper/__init__.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/mat_wrapper/__init__.py',
   'DATA'),
  ('cv2/gapi/__init__.py',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/__init__.py',
   'DATA'),
  ('base_library.zip',
   '/home/<USER>/projects/homework/backend/build/desktop_app/base_library.zip',
   'DATA'),
  ('libquadmath-96973f99.so.0.0.0',
   'numpy.libs/libquadmath-96973f99.so.0.0.0',
   'SYMLINK'),
  ('libopenblas64_p-r0-0cf96a72.3.23.dev.so',
   'numpy.libs/libopenblas64_p-r0-0cf96a72.3.23.dev.so',
   'SYMLINK'),
  ('libgfortran-040039e1.so.5.0.0',
   'numpy.libs/libgfortran-040039e1.so.5.0.0',
   'SYMLINK'),
  ('libXau-154567c4.so.6.0.0',
   'pillow.libs/libXau-154567c4.so.6.0.0',
   'SYMLINK'),
  ('libxcb-64009ff3.so.1.1.0',
   'pillow.libs/libxcb-64009ff3.so.1.1.0',
   'SYMLINK'),
  ('liblzma-64b7ab39.so.5.8.1',
   'pillow.libs/liblzma-64b7ab39.so.5.8.1',
   'SYMLINK'),
  ('libjpeg-8a13c6e0.so.62.4.0',
   'pillow.libs/libjpeg-8a13c6e0.so.62.4.0',
   'SYMLINK'),
  ('libtiff-13a02c81.so.6.1.0',
   'pillow.libs/libtiff-13a02c81.so.6.1.0',
   'SYMLINK'),
  ('libopenjp2-56811f71.so.2.5.3',
   'pillow.libs/libopenjp2-56811f71.so.2.5.3',
   'SYMLINK'),
  ('libwebpmux-6f2b1ad9.so.3.1.1',
   'pillow.libs/libwebpmux-6f2b1ad9.so.3.1.1',
   'SYMLINK'),
  ('libwebp-5f0275c0.so.7.1.10',
   'pillow.libs/libwebp-5f0275c0.so.7.1.10',
   'SYMLINK'),
  ('libsharpyuv-60a7c00b.so.0.1.1',
   'pillow.libs/libsharpyuv-60a7c00b.so.0.1.1',
   'SYMLINK'),
  ('libwebpdemux-efaed568.so.2.0.16',
   'pillow.libs/libwebpdemux-efaed568.so.2.0.16',
   'SYMLINK'),
  ('libavif-01e67780.so.16.3.0',
   'pillow.libs/libavif-01e67780.so.16.3.0',
   'SYMLINK'),
  ('liblcms2-cc10e42f.so.2.0.17',
   'pillow.libs/liblcms2-cc10e42f.so.2.0.17',
   'SYMLINK')],
 [('genericpath', '/usr/lib/python3.12/genericpath.py', 'PYMODULE'),
  ('sre_parse', '/usr/lib/python3.12/sre_parse.py', 'PYMODULE'),
  ('sre_compile', '/usr/lib/python3.12/sre_compile.py', 'PYMODULE'),
  ('io', '/usr/lib/python3.12/io.py', 'PYMODULE'),
  ('linecache', '/usr/lib/python3.12/linecache.py', 'PYMODULE'),
  ('warnings', '/usr/lib/python3.12/warnings.py', 'PYMODULE'),
  ('functools', '/usr/lib/python3.12/functools.py', 'PYMODULE'),
  ('encodings.zlib_codec',
   '/usr/lib/python3.12/encodings/zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   '/usr/lib/python3.12/encodings/uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   '/usr/lib/python3.12/encodings/utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8', '/usr/lib/python3.12/encodings/utf_8.py', 'PYMODULE'),
  ('encodings.utf_7', '/usr/lib/python3.12/encodings/utf_7.py', 'PYMODULE'),
  ('encodings.utf_32_le',
   '/usr/lib/python3.12/encodings/utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   '/usr/lib/python3.12/encodings/utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32', '/usr/lib/python3.12/encodings/utf_32.py', 'PYMODULE'),
  ('encodings.utf_16_le',
   '/usr/lib/python3.12/encodings/utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   '/usr/lib/python3.12/encodings/utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16', '/usr/lib/python3.12/encodings/utf_16.py', 'PYMODULE'),
  ('encodings.unicode_escape',
   '/usr/lib/python3.12/encodings/unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   '/usr/lib/python3.12/encodings/undefined.py',
   'PYMODULE'),
  ('encodings.tis_620', '/usr/lib/python3.12/encodings/tis_620.py', 'PYMODULE'),
  ('encodings.shift_jisx0213',
   '/usr/lib/python3.12/encodings/shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   '/usr/lib/python3.12/encodings/shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   '/usr/lib/python3.12/encodings/shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13', '/usr/lib/python3.12/encodings/rot_13.py', 'PYMODULE'),
  ('encodings.raw_unicode_escape',
   '/usr/lib/python3.12/encodings/raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   '/usr/lib/python3.12/encodings/quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   '/usr/lib/python3.12/encodings/punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154', '/usr/lib/python3.12/encodings/ptcp154.py', 'PYMODULE'),
  ('encodings.palmos', '/usr/lib/python3.12/encodings/palmos.py', 'PYMODULE'),
  ('encodings.oem', '/usr/lib/python3.12/encodings/oem.py', 'PYMODULE'),
  ('encodings.mbcs', '/usr/lib/python3.12/encodings/mbcs.py', 'PYMODULE'),
  ('encodings.mac_turkish',
   '/usr/lib/python3.12/encodings/mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   '/usr/lib/python3.12/encodings/mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   '/usr/lib/python3.12/encodings/mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   '/usr/lib/python3.12/encodings/mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   '/usr/lib/python3.12/encodings/mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   '/usr/lib/python3.12/encodings/mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   '/usr/lib/python3.12/encodings/mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   '/usr/lib/python3.12/encodings/mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   '/usr/lib/python3.12/encodings/mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   '/usr/lib/python3.12/encodings/mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1', '/usr/lib/python3.12/encodings/latin_1.py', 'PYMODULE'),
  ('encodings.kz1048', '/usr/lib/python3.12/encodings/kz1048.py', 'PYMODULE'),
  ('encodings.koi8_u', '/usr/lib/python3.12/encodings/koi8_u.py', 'PYMODULE'),
  ('encodings.koi8_t', '/usr/lib/python3.12/encodings/koi8_t.py', 'PYMODULE'),
  ('encodings.koi8_r', '/usr/lib/python3.12/encodings/koi8_r.py', 'PYMODULE'),
  ('encodings.johab', '/usr/lib/python3.12/encodings/johab.py', 'PYMODULE'),
  ('encodings.iso8859_9',
   '/usr/lib/python3.12/encodings/iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   '/usr/lib/python3.12/encodings/iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   '/usr/lib/python3.12/encodings/iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   '/usr/lib/python3.12/encodings/iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   '/usr/lib/python3.12/encodings/iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   '/usr/lib/python3.12/encodings/iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   '/usr/lib/python3.12/encodings/iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   '/usr/lib/python3.12/encodings/iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   '/usr/lib/python3.12/encodings/iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   '/usr/lib/python3.12/encodings/iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   '/usr/lib/python3.12/encodings/iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   '/usr/lib/python3.12/encodings/iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   '/usr/lib/python3.12/encodings/iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   '/usr/lib/python3.12/encodings/iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   '/usr/lib/python3.12/encodings/iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   '/usr/lib/python3.12/encodings/iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   '/usr/lib/python3.12/encodings/iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   '/usr/lib/python3.12/encodings/iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   '/usr/lib/python3.12/encodings/iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   '/usr/lib/python3.12/encodings/iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   '/usr/lib/python3.12/encodings/iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   '/usr/lib/python3.12/encodings/iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna', '/usr/lib/python3.12/encodings/idna.py', 'PYMODULE'),
  ('encodings.hz', '/usr/lib/python3.12/encodings/hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   '/usr/lib/python3.12/encodings/hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   '/usr/lib/python3.12/encodings/hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk', '/usr/lib/python3.12/encodings/gbk.py', 'PYMODULE'),
  ('encodings.gb2312', '/usr/lib/python3.12/encodings/gb2312.py', 'PYMODULE'),
  ('encodings.gb18030', '/usr/lib/python3.12/encodings/gb18030.py', 'PYMODULE'),
  ('encodings.euc_kr', '/usr/lib/python3.12/encodings/euc_kr.py', 'PYMODULE'),
  ('encodings.euc_jp', '/usr/lib/python3.12/encodings/euc_jp.py', 'PYMODULE'),
  ('encodings.euc_jisx0213',
   '/usr/lib/python3.12/encodings/euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   '/usr/lib/python3.12/encodings/euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950', '/usr/lib/python3.12/encodings/cp950.py', 'PYMODULE'),
  ('encodings.cp949', '/usr/lib/python3.12/encodings/cp949.py', 'PYMODULE'),
  ('encodings.cp932', '/usr/lib/python3.12/encodings/cp932.py', 'PYMODULE'),
  ('encodings.cp875', '/usr/lib/python3.12/encodings/cp875.py', 'PYMODULE'),
  ('encodings.cp874', '/usr/lib/python3.12/encodings/cp874.py', 'PYMODULE'),
  ('encodings.cp869', '/usr/lib/python3.12/encodings/cp869.py', 'PYMODULE'),
  ('encodings.cp866', '/usr/lib/python3.12/encodings/cp866.py', 'PYMODULE'),
  ('encodings.cp865', '/usr/lib/python3.12/encodings/cp865.py', 'PYMODULE'),
  ('encodings.cp864', '/usr/lib/python3.12/encodings/cp864.py', 'PYMODULE'),
  ('encodings.cp863', '/usr/lib/python3.12/encodings/cp863.py', 'PYMODULE'),
  ('encodings.cp862', '/usr/lib/python3.12/encodings/cp862.py', 'PYMODULE'),
  ('encodings.cp861', '/usr/lib/python3.12/encodings/cp861.py', 'PYMODULE'),
  ('encodings.cp860', '/usr/lib/python3.12/encodings/cp860.py', 'PYMODULE'),
  ('encodings.cp858', '/usr/lib/python3.12/encodings/cp858.py', 'PYMODULE'),
  ('encodings.cp857', '/usr/lib/python3.12/encodings/cp857.py', 'PYMODULE'),
  ('encodings.cp856', '/usr/lib/python3.12/encodings/cp856.py', 'PYMODULE'),
  ('encodings.cp855', '/usr/lib/python3.12/encodings/cp855.py', 'PYMODULE'),
  ('encodings.cp852', '/usr/lib/python3.12/encodings/cp852.py', 'PYMODULE'),
  ('encodings.cp850', '/usr/lib/python3.12/encodings/cp850.py', 'PYMODULE'),
  ('encodings.cp775', '/usr/lib/python3.12/encodings/cp775.py', 'PYMODULE'),
  ('encodings.cp737', '/usr/lib/python3.12/encodings/cp737.py', 'PYMODULE'),
  ('encodings.cp720', '/usr/lib/python3.12/encodings/cp720.py', 'PYMODULE'),
  ('encodings.cp500', '/usr/lib/python3.12/encodings/cp500.py', 'PYMODULE'),
  ('encodings.cp437', '/usr/lib/python3.12/encodings/cp437.py', 'PYMODULE'),
  ('encodings.cp424', '/usr/lib/python3.12/encodings/cp424.py', 'PYMODULE'),
  ('encodings.cp273', '/usr/lib/python3.12/encodings/cp273.py', 'PYMODULE'),
  ('encodings.cp1258', '/usr/lib/python3.12/encodings/cp1258.py', 'PYMODULE'),
  ('encodings.cp1257', '/usr/lib/python3.12/encodings/cp1257.py', 'PYMODULE'),
  ('encodings.cp1256', '/usr/lib/python3.12/encodings/cp1256.py', 'PYMODULE'),
  ('encodings.cp1255', '/usr/lib/python3.12/encodings/cp1255.py', 'PYMODULE'),
  ('encodings.cp1254', '/usr/lib/python3.12/encodings/cp1254.py', 'PYMODULE'),
  ('encodings.cp1253', '/usr/lib/python3.12/encodings/cp1253.py', 'PYMODULE'),
  ('encodings.cp1252', '/usr/lib/python3.12/encodings/cp1252.py', 'PYMODULE'),
  ('encodings.cp1251', '/usr/lib/python3.12/encodings/cp1251.py', 'PYMODULE'),
  ('encodings.cp1250', '/usr/lib/python3.12/encodings/cp1250.py', 'PYMODULE'),
  ('encodings.cp1140', '/usr/lib/python3.12/encodings/cp1140.py', 'PYMODULE'),
  ('encodings.cp1125', '/usr/lib/python3.12/encodings/cp1125.py', 'PYMODULE'),
  ('encodings.cp1026', '/usr/lib/python3.12/encodings/cp1026.py', 'PYMODULE'),
  ('encodings.cp1006', '/usr/lib/python3.12/encodings/cp1006.py', 'PYMODULE'),
  ('encodings.cp037', '/usr/lib/python3.12/encodings/cp037.py', 'PYMODULE'),
  ('encodings.charmap', '/usr/lib/python3.12/encodings/charmap.py', 'PYMODULE'),
  ('encodings.bz2_codec',
   '/usr/lib/python3.12/encodings/bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   '/usr/lib/python3.12/encodings/big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5', '/usr/lib/python3.12/encodings/big5.py', 'PYMODULE'),
  ('encodings.base64_codec',
   '/usr/lib/python3.12/encodings/base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii', '/usr/lib/python3.12/encodings/ascii.py', 'PYMODULE'),
  ('encodings.aliases', '/usr/lib/python3.12/encodings/aliases.py', 'PYMODULE'),
  ('encodings', '/usr/lib/python3.12/encodings/__init__.py', 'PYMODULE'),
  ('heapq', '/usr/lib/python3.12/heapq.py', 'PYMODULE'),
  ('stat', '/usr/lib/python3.12/stat.py', 'PYMODULE'),
  ('sre_constants', '/usr/lib/python3.12/sre_constants.py', 'PYMODULE'),
  ('re._parser', '/usr/lib/python3.12/re/_parser.py', 'PYMODULE'),
  ('re._constants', '/usr/lib/python3.12/re/_constants.py', 'PYMODULE'),
  ('re._compiler', '/usr/lib/python3.12/re/_compiler.py', 'PYMODULE'),
  ('re._casefix', '/usr/lib/python3.12/re/_casefix.py', 'PYMODULE'),
  ('re', '/usr/lib/python3.12/re/__init__.py', 'PYMODULE'),
  ('reprlib', '/usr/lib/python3.12/reprlib.py', 'PYMODULE'),
  ('ntpath', '/usr/lib/python3.12/ntpath.py', 'PYMODULE'),
  ('_collections_abc', '/usr/lib/python3.12/_collections_abc.py', 'PYMODULE'),
  ('types', '/usr/lib/python3.12/types.py', 'PYMODULE'),
  ('copyreg', '/usr/lib/python3.12/copyreg.py', 'PYMODULE'),
  ('collections.abc', '/usr/lib/python3.12/collections/abc.py', 'PYMODULE'),
  ('collections', '/usr/lib/python3.12/collections/__init__.py', 'PYMODULE'),
  ('traceback', '/usr/lib/python3.12/traceback.py', 'PYMODULE'),
  ('enum', '/usr/lib/python3.12/enum.py', 'PYMODULE'),
  ('abc', '/usr/lib/python3.12/abc.py', 'PYMODULE'),
  ('operator', '/usr/lib/python3.12/operator.py', 'PYMODULE'),
  ('posixpath', '/usr/lib/python3.12/posixpath.py', 'PYMODULE'),
  ('weakref', '/usr/lib/python3.12/weakref.py', 'PYMODULE'),
  ('locale', '/usr/lib/python3.12/locale.py', 'PYMODULE'),
  ('_weakrefset', '/usr/lib/python3.12/_weakrefset.py', 'PYMODULE'),
  ('keyword', '/usr/lib/python3.12/keyword.py', 'PYMODULE'),
  ('codecs', '/usr/lib/python3.12/codecs.py', 'PYMODULE'),
  ('cv2',
   '/usr/local/lib/python3.12/dist-packages/cv2/__init__.py',
   'PYMODULE'),
  ('cv2.utils',
   '/usr/local/lib/python3.12/dist-packages/cv2/utils/__init__.py',
   'PYMODULE'),
  ('cv2.typing',
   '/usr/local/lib/python3.12/dist-packages/cv2/typing/__init__.py',
   'PYMODULE'),
  ('cv2.dnn', '-', 'PYMODULE'),
  ('cv2.gapi.wip.draw', '-', 'PYMODULE'),
  ('cv2.gapi.wip', '-', 'PYMODULE'),
  ('cv2.misc.version',
   '/usr/local/lib/python3.12/dist-packages/cv2/misc/version.py',
   'PYMODULE'),
  ('cv2.misc',
   '/usr/local/lib/python3.12/dist-packages/cv2/misc/__init__.py',
   'PYMODULE'),
  ('cv2.mat_wrapper',
   '/usr/local/lib/python3.12/dist-packages/cv2/mat_wrapper/__init__.py',
   'PYMODULE'),
  ('cv2.gapi',
   '/usr/local/lib/python3.12/dist-packages/cv2/gapi/__init__.py',
   'PYMODULE'),
  ('cv2.config',
   '/usr/local/lib/python3.12/dist-packages/cv2/config.py',
   'PYMODULE'),
  ('cv2.load_config_py3',
   '/usr/local/lib/python3.12/dist-packages/cv2/load_config_py3.py',
   'PYMODULE'),
  ('os', '/usr/lib/python3.12/os.py', 'PYMODULE')])
