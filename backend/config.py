"""
应用配置文件
"""
import os

# 文件上传配置
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'output'
THUMBNAIL_FOLDER = 'thumbnails'  # 缩略图存储目录
MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB max file size

# 安全配置
ALLOWED_EXTENSIONS = {
    'image': {'jpg', 'jpeg', 'png', 'bmp', 'tiff', 'webp'},
    'video': {'mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv'}
}

# CORS配置
CORS_ORIGINS = [
    'http://localhost:3000',
    'http://127.0.0.1:3000',
    'http://localhost:3001',  # Vite实际运行端口
    'http://127.0.0.1:3001',
    'http://localhost:5173',  # Vite默认端口
    'http://127.0.0.1:5173'
]

# Celery配置
CELERY_BROKER_URL = os.environ.get('CELERY_BROKER_URL', 'redis://localhost:6379/0')
CELERY_RESULT_BACKEND = os.environ.get('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')

CELERY_CONFIG = {
    'broker_url': CELERY_BROKER_URL,
    'result_backend': CELERY_RESULT_BACKEND,
    'task_serializer': 'json',
    'accept_content': ['json'],
    'result_serializer': 'json',
    'timezone': 'UTC',
    'enable_utc': True,
    'result_expires': 3600,
    # 并发控制配置
    'worker_concurrency': 4,  # 限制worker并发数
    'worker_prefetch_multiplier': 1,  # 每个worker一次只处理一个任务
    'task_acks_late': True,  # 任务完成后才确认
    'worker_disable_rate_limits': False,  # 启用速率限制
    'broker_connection_retry_on_startup': True,  # 启动时重试连接
}

# 确保目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)
os.makedirs(THUMBNAIL_FOLDER, exist_ok=True)
