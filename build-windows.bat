@echo off
echo ========================================
echo 文件处理器桌面应用 - Windows构建脚本
echo ========================================

:: 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Node.js，请先安装Node.js
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

:: 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.12+
    echo 下载地址: https://python.org/
    pause
    exit /b 1
)

echo.
echo [1/4] 构建Python后端...
echo ========================================

cd backend

:: 创建虚拟环境（如果不存在）
if not exist ".venv" (
    echo 创建Python虚拟环境...
    python -m venv .venv
)

:: 激活虚拟环境
call .venv\Scripts\activate.bat

:: 安装依赖
echo 安装Python依赖...
pip install -r requirements.txt
pip install pyinstaller

:: 打包后端
echo 打包Python后端...
pyinstaller desktop_app.spec --clean

if not exist "dist\FileProcessorBackendSync.exe" (
    echo 错误: 后端打包失败
    pause
    exit /b 1
)

echo 后端打包完成！

echo.
echo [2/4] 构建React前端...
echo ========================================

cd ..\frontend

:: 安装前端依赖
echo 安装前端依赖...
npm install

:: 构建前端
echo 构建前端...
npm run build

if not exist "dist\index.html" (
    echo 错误: 前端构建失败
    pause
    exit /b 1
)

echo 前端构建完成！

echo.
echo [2.5/4] 修复前端路径问题...
echo ========================================

:: 应用前端路径修复
if exist "..\electron-app\main-fixed.js" (
    echo 应用修复后的main.js...
    copy "..\electron-app\main-fixed.js" "..\electron-app\main.js" >nul
    echo 前端路径修复完成！
) else (
    echo 警告: 未找到main-fixed.js，跳过路径修复
)

echo.
echo [3/4] 构建Electron应用...
echo ========================================

cd ..\electron-app

:: 安装Electron依赖
echo 安装Electron依赖...
npm install

:: 构建桌面应用
echo 构建桌面应用...
npm run dist

if not exist "release" (
    echo 错误: Electron应用构建失败
    pause
    exit /b 1
)

echo.
echo [4/4] 构建完成！
echo ========================================

echo.
echo 构建产物位置:
echo - 安装包: electron-app\release\
echo - 后端可执行文件: backend\dist\FileProcessorBackendSync.exe
echo - 前端文件: frontend\dist\

echo.
echo 您可以运行以下文件来测试应用:
dir electron-app\release\*.exe 2>nul
if errorlevel 1 (
    echo 未找到Windows安装包，请检查构建日志
) else (
    echo 找到Windows安装包，可以直接安装使用！
)

echo.
echo 构建完成！按任意键退出...
pause >nul
