# 文件处理器 Docker 部署指南

## 🚀 快速开始

### 一键部署（推荐）
```bash
# 克隆或下载项目到本地
# 进入项目目录
cd homework

# 一键部署
./deploy.sh
```

### 手动部署
```bash
# 1. 启动所有服务
docker-compose up -d

# 2. 查看服务状态
docker-compose ps

# 3. 查看日志
docker-compose logs -f
```

## 📋 系统要求

- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **内存**: 最少 4GB RAM
- **存储**: 最少 10GB 可用空间
- **操作系统**: Linux, macOS, Windows

## 🏗️ 架构说明

### 服务组件
```
┌─────────────────┐    ┌─────────────────┐
│   Nginx (80)    │────│  Frontend (3000) │
│   反向代理       │    │   React 应用     │
└─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐    ┌─────────────────┐
│  Backend (5000) │────│   Redis (6379)  │
│   Flask API     │    │   消息队列       │
└─────────────────┘    └─────────────────┘
         │
         ▼
┌─────────────────┐
│ Celery Worker   │
│   任务处理       │
└─────────────────┘
```

### 端口映射
- **80**: Nginx 主入口
- **3000**: 前端应用（直接访问）
- **5000**: 后端 API（直接访问）
- **6379**: Redis（内部访问）

## 🔧 配置说明

### 环境变量
编辑 `.env` 文件来自定义配置：

```bash
# 端口配置
FRONTEND_PORT=3000
BACKEND_PORT=5000
NGINX_PORT=80

# Redis 配置
REDIS_URL=redis://redis:6379/0

# 文件上传限制
MAX_CONTENT_LENGTH=104857600  # 100MB

# Celery 工作进程数
CELERY_WORKER_CONCURRENCY=4
```

### 数据持久化
以下目录会被持久化存储：
- `./backend/uploads/` - 上传的文件
- `./backend/output/` - 处理后的文件
- `./backend/logs/` - 应用日志
- `redis_data` - Redis 数据

## 📱 访问应用

部署完成后，您可以通过以下地址访问：

### 主要入口
- **主应用**: http://localhost
- **前端应用**: http://localhost:3000
- **后端 API**: http://localhost:5000

### 健康检查
- **应用健康**: http://localhost/health
- **API 健康**: http://localhost:5000/api/health

### API 文档
- **操作列表**: http://localhost:5000/api/operations
- **系统信息**: http://localhost:5000/api/system/info

## 🛠️ 管理命令

### 基本操作
```bash
# 启动服务
./deploy.sh start
# 或
docker-compose up -d

# 停止服务
./deploy.sh stop
# 或
docker-compose down

# 重启服务
./deploy.sh restart
# 或
docker-compose restart

# 查看状态
./deploy.sh status
# 或
docker-compose ps
```

### 日志管理
```bash
# 查看所有服务日志
./deploy.sh logs
# 或
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f celery-worker
docker-compose logs -f redis
```

### 数据管理
```bash
# 备份上传文件
tar -czf uploads-backup.tar.gz backend/uploads/

# 备份输出文件
tar -czf output-backup.tar.gz backend/output/

# 清理临时文件
docker-compose exec backend find /app/uploads -type f -mtime +7 -delete
```

## 🔍 故障排除

### 常见问题

#### 1. 端口被占用
```bash
# 检查端口占用
netstat -tulpn | grep :80
netstat -tulpn | grep :3000
netstat -tulpn | grep :5000

# 修改端口（编辑 .env 文件）
NGINX_PORT=8080
FRONTEND_PORT=3001
BACKEND_PORT=5001
```

#### 2. 内存不足
```bash
# 检查内存使用
docker stats

# 减少 Celery 工作进程
# 编辑 .env 文件
CELERY_WORKER_CONCURRENCY=2
```

#### 3. 文件权限问题
```bash
# 修复文件权限
sudo chown -R $USER:$USER backend/uploads backend/output backend/logs
chmod -R 755 backend/uploads backend/output backend/logs
```

#### 4. Redis 连接失败
```bash
# 检查 Redis 状态
docker-compose exec redis redis-cli ping

# 重启 Redis
docker-compose restart redis
```

### 调试模式

#### 开发环境部署
```bash
# 使用开发配置
docker-compose -f docker-compose.dev.yml up -d

# 查看开发环境日志
docker-compose -f docker-compose.dev.yml logs -f
```

#### 进入容器调试
```bash
# 进入后端容器
docker-compose exec backend bash

# 进入前端容器
docker-compose exec frontend sh

# 进入 Redis 容器
docker-compose exec redis redis-cli
```

## 🔒 生产环境配置

### 安全配置
1. **修改默认密钥**：
   ```bash
   # 编辑 .env 文件
   SECRET_KEY=your-very-secure-secret-key
   ```

2. **启用 HTTPS**：
   ```bash
   # 将 SSL 证书放入 nginx/ssl/ 目录
   # 取消注释 nginx/nginx.conf 中的 HTTPS 配置
   ```

3. **限制访问**：
   ```bash
   # 修改 nginx/nginx.conf 添加 IP 白名单
   allow ***********/24;
   deny all;
   ```

### 性能优化
1. **增加工作进程**：
   ```bash
   # 编辑 .env 文件
   CELERY_WORKER_CONCURRENCY=8
   ```

2. **启用缓存**：
   ```bash
   # 在 nginx 配置中启用更多缓存
   ```

## 📊 监控和维护

### 健康检查
```bash
# 自动健康检查脚本
#!/bin/bash
curl -f http://localhost/health || echo "Service down!"
```

### 日志轮转
```bash
# 添加到 crontab
0 2 * * * docker-compose exec backend find /app/logs -name "*.log" -mtime +30 -delete
```

### 备份策略
```bash
# 每日备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d)
tar -czf backup-$DATE.tar.gz backend/uploads backend/output
```

## 📞 技术支持

如果遇到问题，请检查：
1. Docker 和 Docker Compose 版本
2. 系统资源（内存、磁盘空间）
3. 端口占用情况
4. 服务日志输出

更多帮助请查看项目文档或提交 Issue。
